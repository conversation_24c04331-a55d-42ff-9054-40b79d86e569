#!/bin/bash

# Test script for the new Company List Inserts API
# This script demonstrates how to use the new /institution/{institution_id}/inserts endpoint

# Base URL - replace with your actual API Gateway URL
BASE_URL="https://94gnwh0cr9.execute-api.ap-south-1.amazonaws.com/dev"

echo "Testing Company List Inserts API"
echo "================================="

# Test 1: Get all inserts for institution 1 (no filters)
echo -e "\n1. Getting all inserts for institution 1 (no filters):"
curl -X GET "${BASE_URL}/institution/1/inserts" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 2: Get inserts with status filter
echo -e "\n\n2. Getting inserts with status filter (Success, Processing):"
curl -X GET "${BASE_URL}/institution/1/inserts?status=Success,Processing" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 3: Get inserts with identifier type filter
echo -e "\n\n3. Getting inserts with identifier type filter (Aadhar ID, PAN ID):"
curl -X GET "${BASE_URL}/institution/1/inserts?identifier_type=Aadhar%20ID,PAN%20ID" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 4: Get inserts with fraud type filter
echo -e "\n\n4. Getting inserts with fraud type filter (Mule, Victim):"
curl -X GET "${BASE_URL}/institution/1/inserts?fraud_type=Mule,Victim" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 5: Get inserts with fraud status filter
echo -e "\n\n5. Getting inserts with fraud status filter (Confirmed, Suspected):"
curl -X GET "${BASE_URL}/institution/1/inserts?fraud_status=Confirmed,Suspected" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 6: Get inserts for different institution (ID: 2)
echo -e "\n\n6. Getting inserts for different institution (ID: 2):"
curl -X GET "${BASE_URL}/institution/2/inserts" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 7: Get inserts with sorting (by created_at descending)
echo -e "\n\n7. Getting inserts sorted by creation date (newest first):"
curl -X GET "${BASE_URL}/institution/1/inserts?sort_by=created_at&sort_order=desc" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 8: Get inserts with pagination
echo -e "\n\n8. Getting inserts with pagination (limit: 5, offset: 0):"
curl -X GET "${BASE_URL}/institution/1/inserts?limit=5&offset=0" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 9: Complex filter combination
echo -e "\n\n9. Complex filter combination (status + fraud_type + sorting + pagination):"
curl -X GET "${BASE_URL}/institution/1/inserts?status=Success&fraud_type=Mule&sort_by=created_at&sort_order=desc&limit=3" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

# Test 10: All filters from the UI image
echo -e "\n\n10. All filters from UI (Status: Success, Identifier Type: Aadhar ID, Fraud Type: Mule, Fraud Status: Confirmed):"
curl -X GET "${BASE_URL}/institution/1/inserts?status=Success&identifier_type=Aadhar%20ID&fraud_type=Mule&fraud_status=Confirmed" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\nAPI Testing completed!"
echo "Note: Replace the BASE_URL with your actual API Gateway URL if different." 