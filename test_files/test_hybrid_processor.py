#!/usr/bin/env python3
"""
Test script for the hybrid SMS processor.
"""

import asyncio
import csv
import json
from hybrid_sms_processor import HybridSMSProcessor

async def test_hybrid_processor():
    """Test the hybrid processor with sample messages."""
    
    # Initialize processor without OpenAI (rule-based only for testing)
    processor = HybridSMSProcessor()
    
    # Test messages - mix of financial and non-financial
    test_messages = [
        {
            'original_id': 'test1',
            'phone_number': 'xx12345678',
            'sender_address': 'SBI',
            'timestamp': '2024-01-01 12:00:00',
            'original_text': 'Rs.1000 debited from A/c X1234 on 01-Jan-24 at SBI ATM. Avl Bal: Rs.5000'
        },
        {
            'original_id': 'test2',
            'phone_number': 'xx12345678',
            'sender_address': 'HDFC',
            'timestamp': '2024-01-01 13:00:00',
            'original_text': 'Rs.2500 credited to A/c X5678 on 01-Jan-24. Salary from COMPANY ABC. Bal: Rs.15000'
        },
        {
            'original_id': 'test3',
            'phone_number': 'xx12345678',
            'sender_address': 'JioSvc',
            'timestamp': '2024-01-01 14:00:00',
            'original_text': 'Your Jio number is ready for use. Download MyJio app for best experience.'
        },
        {
            'original_id': 'test4',
            'phone_number': 'xx12345678',
            'sender_address': 'PAYTM',
            'timestamp': '2024-01-01 15:00:00',
            'original_text': 'Rs.500 paid to ZOMATO via UPI. Txn ID: *********. Wallet balance: Rs.1000'
        },
        {
            'original_id': 'test5',
            'phone_number': 'xx12345678',
            'sender_address': 'OFFERS',
            'timestamp': '2024-01-01 16:00:00',
            'original_text': 'Get 50% cashback on your next purchase! Use code SAVE50. Valid till 31st Jan.'
        }
    ]
    
    print("Testing Hybrid SMS Processor...")
    print("=" * 50)
    
    results = []
    for i, message in enumerate(test_messages, 1):
        print(f"\nTest {i}: Processing message from {message['sender_address']}")
        print(f"Text: {message['original_text'][:60]}{'...' if len(message['original_text']) > 60 else ''}")
        
        try:
            result = await processor.process_sms(message)
            results.append(result)
            
            print(f"Classification: {result.get('classification', 'unknown')}")
            print(f"SMS Type: {result.get('sms_type', 'unknown')}")
            print(f"Event Subtype: {result.get('sms_event_subtype', 'unknown')}")
            print(f"Amount: {result.get('amount', 'N/A')}")
            print(f"Entity: {result.get('entity_name', 'N/A')}")
            print(f"Status: {result.get('processing_status', 'unknown')}")
            
            if result.get('error_message'):
                print(f"Error: {result['error_message']}")
                
        except Exception as e:
            print(f"ERROR processing message: {e}")
            results.append({
                'original_id': message['original_id'],
                'error': str(e)
            })
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    financial_count = sum(1 for r in results if r.get('classification') == 'financial')
    non_financial_count = sum(1 for r in results if r.get('classification') == 'non-financial')
    error_count = sum(1 for r in results if r.get('processing_status') == 'error' or 'error' in r)
    
    print(f"Total messages processed: {len(results)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    print(f"Errors: {error_count}")
    
    # Save test results to CSV
    if results:
        # Get all field names
        all_fields = set()
        for result in results:
            all_fields.update(result.keys())
        
        fieldnames = sorted(list(all_fields))
        
        with open('test_hybrid_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print(f"\nTest results saved to 'test_hybrid_results.csv'")
    
    return results

if __name__ == "__main__":
    asyncio.run(test_hybrid_processor())
