account_number,amount,available_balance,bank_name,classification,classified_infotype,classified_subtype,classified_type,currency,date,entity_name,error_message,extracted_data_json,original_id,original_text,phone_number,processing_status,sender_address,sms_event_subtype,sms_info_type,sms_type,timestamp,transaction_amount,txn_ref
X1234,1000,5000,SBI,financial,,,Purchase,INR,,SBI,,"{""transaction_amount"": ""1000"", ""currency"": ""INR"", ""entity_name"": ""SBI"", ""card_number"": null, ""account_number"": ""X1234"", ""txn_ref"": null, ""available_balance"": ""5000""}",openai_test1,Rs.1000 debited from A/c X1234 on 01-Jan-24 at SBI ATM. Avl Bal: Rs.5000,xx12345678,success,SBI,Debit Card,Outflow,Purchase,2024-01-01 12:00:00,1000,
,,,,non-financial,,,Non Financial,,,,,{},openai_test2,Rs.2500 credited to A/c X5678 on 01-Jan-24. Salary from COMPANY ABC. Bal: Rs.15000,xx12345678,success,HDFC,Non-Financial,Other,Other,2024-01-01 13:00:00,,
,500,1000,ZOMATO,financial,,,Purchase,INR,,ZOMATO,,"{""transaction_amount"": ""500"", ""currency"": ""INR"", ""entity_name"": ""ZOMATO"", ""card_number"": null, ""account_number"": null, ""txn_ref"": ""*********"", ""available_balance"": ""1000""}",openai_test3,Rs.500 paid to ZOMATO via UPI. Txn ID: *********. Wallet balance: Rs.1000,xx12345678,success,PAYTM,UPI,Outflow,Purchase,2024-01-01 15:00:00,500,*********
,,,,financial,,,Non Financial,INR,,,,"{""entity_name"": null}",openai_test4,Your Jio number is ready for use. Download MyJio app for best experience.,xx12345678,success,JioSvc,Unknown,Other,Other,2024-01-01 14:00:00,,
