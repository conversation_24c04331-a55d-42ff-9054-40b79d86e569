# Environment files containing sensitive keys
.env
.env.local
.env.*.local
configs/.env.*

# Go build artifacts
/secure_double_blind_ec
*.exe
*.exe~
*.dll
*.so
*.dylib

# Lambda build artifacts
lambdas/*/bootstrap
lambdas/*/*.zip
*.zip

# SAM
.aws-sam/
samconfig.toml.bak

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html

# Dependency directories
vendor/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Deployment info
deployment-info.txt

# Database
*.sql.bak
*.db

# Temporary files
tmp/
temp/
.tmp/

# Local test data
test-data/
*.json.bak
vendor/

# Go workspace file
go.work
