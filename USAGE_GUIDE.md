# SMS Backup Processing - Quick Usage Guide

## 🚀 Quick Start

### 1. Process your sms_backup.csv file
```bash
python3 run_sms_processing.py
```
This automatically detects and processes `sms_backup.csv` if it exists.

### 2. Process a specific file
```bash
python3 run_sms_processing.py your_sms_file.csv
```

### 3. Specify both input and output files
```bash
python3 run_sms_processing.py input.csv output_results.csv
```

### 4. Test with sample data
```bash
python3 test_sms_processing.py
```

### 5. See complete demo
```bash
python3 demo_complete_workflow.py
```

## 📊 Output Structure

The generated CSV contains:

| Column | Description | Example |
|--------|-------------|---------|
| `classification` | financial/non-financial | financial |
| `sms_type` | Main category | Purchase |
| `sms_event_subtype` | Subcategory | UPI |
| `sms_info_type` | Info type | Outflow |
| `amount` | Extracted amount | 1500 |
| `date` | Transaction date | 01-01-2024 |
| `account_number` | Account number | **1234 |
| `extracted_data_json` | All data as JSON | {"amount":"1500",...} |

## 🏷️ Classification Categories

### Financial Types
- **Purchase**: UPI, Debit Card, Credit Card
- **Payment**: EMI Payment, Recharge  
- **Deposit & Withdrawal**: Salary Credit, Loan Disbursal, ATM Withdrawal
- **Accounts**: Bank Account, Loan Account
- **Investment**: Trading, Mutual Funds, SIP

### Info Types
- **Outflow**: Money going out (debits, payments)
- **Inflow**: Money coming in (credits, salary)
- **Account Status**: Balance updates, account info
- **Application**: Loan applications, requests

## 📈 Example Results

```csv
classification,sms_type,sms_event_subtype,sms_info_type,amount,date
financial,Purchase,UPI,Outflow,1500,01-01-2024
financial,Payment,EMI Payment,Outflow,8500,01-01-2024
financial,Deposit & Withdrawal,Monthly Salary Credit,Inflow,95000,01-01-2024
non-financial,Other,Non-Financial,Other,,,
```

## ✅ Features

- ✅ Processes large CSV files efficiently
- ✅ Handles both financial and non-financial SMS
- ✅ Extracts structured data (amounts, dates, accounts)
- ✅ Generates comprehensive summary reports
- ✅ Error handling and progress tracking
- ✅ Works with Indian banking SMS formats
- ✅ Concurrent processing for speed

## 🎯 Ready to Use!

Just place your `sms_backup.csv` file in the directory and run:
```bash
python3 run_sms_processing.py
```

The system will generate:
- `sms_processed_results.csv` - Main results
- `sms_processed_results_summary.json` - Statistics
