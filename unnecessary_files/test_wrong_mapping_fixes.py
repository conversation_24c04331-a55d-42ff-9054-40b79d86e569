#!/usr/bin/env python3
"""
Test script to verify that all wrongly marked messages from wrong_mapping.csv are now correctly classified.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def test_wrong_mapping_fixes():
    """Test all the wrongly marked messages to ensure they're now correctly classified."""
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    # Messages that were wrongly marked as NON-FINANCIAL (should be FINANCIAL)
    should_be_financial = [
        # ATM/Debit Card Transactions
        "Dear Customer, transaction number ************ for Rs.6026.00 by SBI Debit Card X3804 at ATM CANARA BANK ATM KPHB on 30-Apr-24 at 18:47. Updated Available Balance is Rs.1,23,456.78. If not u? call **********. -SBI",
        
        # UPI Credits
        "Dear SBI UPI User, ur A/cX4884 credited by Rs6000 on 01May24 trf from VAMSHI KRISHNA MADDULA Refno ************. If not u? call **********. -S<PERSON>",
        "Dear UPI user A/C X4884 credited by Rs.2000.0 on date 30Apr24 trf from VAMSHI KRISHNA MADDULA Refno ************. If not u? call **********. -SBI",
        
        # EMI Bookings
        "Your EMI Booking amount of Rs.36866.65 at VIVO Mobiles has been converted to 6 EMIs at 0% interest rate. EMI amount is Rs.6144.44. First EMI will be charged on 06/05/2024. -SBI Card",
        
        # Credit Card Transactions
        "Rs.274.40 spent on your SBI Credit Card ending 4465 at BOOKMYSHOW on 25-04-24. Available Credit Limit Rs.1,23,456.78. Call ******** for any dispute. -SBI Card",
        "USD600.00 spent on your SBI Credit Card ending 4465 at OPENAI on 06-05-24. Available Credit Limit Rs.1,23,456.78. Call ******** for any dispute. -SBI Card",
        
        # E-mandate/Cardholder Messages
        "Dear Cardholder, your payment of USD 23.60 at OpenAILLC is due on 06/05/2024. To avoid late payment charges, please pay by the due date. For payment options, visit www.sbicard.com or call ********. -SBI Card",
        
        # Credit Card Statements
        "E-statement of SBI Credit Card ending XX65 dated 16/05/2024 is ready. Total Amt Due Rs 13293, Min Amt Due Rs 399. Due Date 10/06/2024. To view, visit www.sbicard.com -SBI Card",
        
        # Loan Overdue
        "Your RBL Bank Loan A/C ****1772 has an overdue amt of Rs. 2661. Pay now to avoid penal charges. Pay via NetBanking/MobileBanking/Branch. For queries call 022-********. -RBL Bank",
        
        # PayPal Credits
        "INR 1,494.23 credited to your A/c No XX4884 on 30-Apr-24 by PAYPAL PAYMENTS PTE LTD Refno ************. If not u? call **********. -SBI",
    ]
    
    # Messages that were wrongly marked as FINANCIAL (should be NON-FINANCIAL)
    should_be_non_financial = [
        # Promotional Network Messages
        "Experience India's only true 5G network on Vi. Enjoy blazing fast speeds, crystal clear voice calls & seamless connectivity. Upgrade to Vi 5G starting at just Rs. 349 per month. Visit your nearest Vi store today!",
        
        # Delivery Tracking (not actual transactions)
        "Your order has been shipped and is out for delivery. Tracking ID: ABC123. Delivery charges: Rs.50. Expected delivery: Today by 6 PM. Track your order at example.com/track",
        
        # Loan Promotional Offers (not actual loans)
        "Congratulations! Your pre-approved personal loan of Rs.5,00,000 is ready. Get it directly disbursed to your account in just 2 minutes. Apply now at quickloan.com/apply. Offer expires soon!",
    ]
    
    print("Testing Wrong Mapping Fixes...")
    print("=" * 80)
    
    # Test messages that should be financial
    print("\n💰 TESTING MESSAGES THAT SHOULD BE FINANCIAL")
    print("-" * 60)
    
    financial_correct = 0
    for i, message in enumerate(should_be_financial, 1):
        print(f"\nFinancial Test {i}: {message[:70]}{'...' if len(message) > 70 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Amount extracted: '{amount}'")
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] != 'Other':
            print("✅ CORRECT: Classified as financial")
            financial_correct += 1
        else:
            print("❌ ERROR: Should be financial")
    
    # Test messages that should be non-financial
    print(f"\n🚫 TESTING MESSAGES THAT SHOULD BE NON-FINANCIAL")
    print("-" * 60)
    
    non_financial_correct = 0
    for i, message in enumerate(should_be_non_financial, 1):
        print(f"\nNon-Financial Test {i}: {message[:70]}{'...' if len(message) > 70 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Amount extracted: '{amount}'")
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] == 'Other':
            print("✅ CORRECT: Classified as non-financial")
            non_financial_correct += 1
        else:
            print("❌ ERROR: Should be non-financial")
    
    # Summary
    total_tests = len(should_be_financial) + len(should_be_non_financial)
    total_correct = financial_correct + non_financial_correct
    
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Should be Financial: {financial_correct}/{len(should_be_financial)} correct ({financial_correct/len(should_be_financial)*100:.1f}%)")
    print(f"Should be Non-Financial: {non_financial_correct}/{len(should_be_non_financial)} correct ({non_financial_correct/len(should_be_non_financial)*100:.1f}%)")
    print(f"Overall: {total_correct}/{total_tests} correct ({total_correct/total_tests*100:.1f}%)")
    
    if total_correct == total_tests:
        print("\n🎉 ALL TESTS PASSED! Wrong mapping issues have been fixed.")
    else:
        print(f"\n⚠️  {total_tests - total_correct} tests failed. Please review the classification logic.")
        
        # Show failed cases
        print("\nFailed cases need attention:")
        if financial_correct < len(should_be_financial):
            print("- Some messages that should be financial are still classified as non-financial")
        if non_financial_correct < len(should_be_non_financial):
            print("- Some messages that should be non-financial are still classified as financial")

if __name__ == "__main__":
    asyncio.run(test_wrong_mapping_fixes())
