#!/usr/bin/env python3
"""
Script to fix all wrongly marked classifications identified in wrong_mapping.csv.
"""

import csv
import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def fix_wrong_mapping_classifications():
    """Fix all wrongly marked classifications from wrong_mapping.csv."""
    
    print("Loading wrong_mapping.csv file...")
    
    # Read the wrong_mapping.csv file
    wrong_mappings = {}
    with open('wrong_mapping.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['Comments'] and 'Wrongly marked' in row['Comments']:
                wrong_mappings[row['original_id']] = {
                    'text': row['original_text'],
                    'current_classification': row['classification'],
                    'comment': row['Comments']
                }
    
    print(f"Found {len(wrong_mappings)} wrongly marked messages in wrong_mapping.csv")
    
    # Read the main corrected CSV file
    rows = []
    with open('sms_processed_results_corrected.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        for row in reader:
            rows.append(row)
    
    print(f"Total records in main file: {len(rows)}")
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    fixed_count = 0
    fixed_examples = []
    
    print("Processing wrongly marked messages...")
    
    for i, row in enumerate(rows):
        original_id = row['original_id']
        
        if original_id in wrong_mappings:
            original_text = row['original_text']
            wrong_info = wrong_mappings[original_id]
            
            print(f"\nProcessing: {original_text[:80]}{'...' if len(original_text) > 80 else ''}")
            print(f"Comment: {wrong_info['comment']}")
            
            # Determine correct classification based on comment
            if 'Wrongly marked as non-financial' in wrong_info['comment']:
                # Should be financial - reclassify and extract fields
                classification = classifier.classify_sms(original_text)
                
                # Extract financial fields
                amount = await extractor.extract_amount(original_text)
                date = await extractor.extract_date(original_text)
                account_number = await extractor.extract_account_number(original_text)
                bank_name = await extractor.extract_bank_name(original_text)
                txn_ref = await extractor.extract_transaction_ref(original_text)
                currency = await extractor.extract_currency(original_text)
                
                # Validate classification
                validated_classification = classifier.validate_financial_classification(classification, amount)
                
                if validated_classification['sms_type'] != 'Other':
                    # Update as financial
                    rows[i]['classification'] = 'financial'
                    rows[i]['sms_type'] = validated_classification['sms_type']
                    rows[i]['sms_event_subtype'] = validated_classification['sms_event_subtype']
                    rows[i]['sms_info_type'] = validated_classification['sms_info_type']
                    rows[i]['amount'] = amount or ''
                    rows[i]['date'] = date or ''
                    rows[i]['account_number'] = account_number or ''
                    rows[i]['bank_name'] = bank_name or ''
                    rows[i]['txn_ref'] = txn_ref or ''
                    rows[i]['currency'] = currency or 'INR'
                    
                    # Create extracted data JSON
                    extracted_data = {
                        'sms_type': validated_classification['sms_type'],
                        'sms_event_subtype': validated_classification['sms_event_subtype'],
                        'sms_info_type': validated_classification['sms_info_type'],
                        'amount': amount or '',
                        'date': date or '',
                        'account_number': account_number or '',
                        'bank_name': bank_name or '',
                        'txn_ref': txn_ref or '',
                        'currency': currency or 'INR'
                    }
                    
                    import json
                    rows[i]['extracted_data_json'] = json.dumps(extracted_data, ensure_ascii=False)
                    
                    fixed_count += 1
                    print(f"✅ FIXED: Reclassified as financial - {validated_classification}")
                    
                    if len(fixed_examples) < 5:
                        fixed_examples.append(f"NON→FIN: {original_text[:60]}...")
                else:
                    print(f"⚠️  Could not reclassify as financial: {original_text[:60]}...")
                    
            elif 'Wrongly marked as financial' in wrong_info['comment']:
                # Should be non-financial
                rows[i]['classification'] = 'non-financial'
                rows[i]['sms_type'] = 'Other'
                rows[i]['sms_event_subtype'] = 'Non-Financial'
                rows[i]['sms_info_type'] = 'Other'
                rows[i]['extracted_data_json'] = '{}'
                rows[i]['amount'] = ''
                rows[i]['date'] = ''
                rows[i]['account_number'] = ''
                rows[i]['bank_name'] = ''
                rows[i]['txn_ref'] = ''
                rows[i]['currency'] = ''
                
                fixed_count += 1
                print(f"✅ FIXED: Reclassified as non-financial")
                
                if len(fixed_examples) < 10:
                    fixed_examples.append(f"FIN→NON: {original_text[:60]}...")
    
    print(f"\nFixed {fixed_count} messages total")
    
    # Save the corrected CSV
    output_file = 'sms_processed_results_final_corrected.csv'
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"Saved corrected data to {output_file}")
    
    # Print summary statistics
    financial_count = sum(1 for row in rows if row['classification'] == 'financial')
    non_financial_count = sum(1 for row in rows if row['classification'] == 'non-financial')
    
    print("\nSummary after fixes:")
    print(f"Total records: {len(rows)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    
    # Show some examples of fixed messages
    print("\nExamples of fixed messages:")
    for example in fixed_examples:
        print(f"- {example}")
    
    if fixed_count > 0:
        print(f"\n✅ SUCCESS: {fixed_count} wrongly marked messages have been corrected!")
    else:
        print("\n✅ No messages needed fixing - all classifications are correct!")

if __name__ == "__main__":
    asyncio.run(fix_wrong_mapping_classifications())
