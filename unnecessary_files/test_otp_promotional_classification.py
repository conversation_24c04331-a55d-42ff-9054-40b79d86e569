#!/usr/bin/env python3
"""
Test script to verify OTP and Promotional Credit messages are classified as non-financial.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def test_otp_and_promotional_classification():
    """Test OTP and promotional credit classification."""
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    # Test OTP messages
    otp_messages = [
        "DO NOT SHARE: Your Google Pay OTP is 904958. Message ID: f6f01vO3FNp",
        "8549 is your CRED verification OTP. The code will be valid for 10 min. Do not share this OTP with anyone.",
        "Use OTP 965945 to log into your Swiggy account. Do not share the OTP or your number with anyone including Swiggy personnel.",
        "OTP for your SBI Card no. 4465 to Reset Website Account password is 803884. This OTP is valid for one Trxn. or 30 mins only.",
        "479462 is the OTP to link mobile device with SBI Card App,for card ending XX4465.OTP is valid for 30 mins only.",
        "851023 is your OTP / verification code for <PERSON><PERSON> and is valid for 5 minutes. Do not share this with anyone. -<PERSON>dha",
        "000450 is your Amazon OTP. Do not share it with anyone.",
        "248105 is your OTP to access DigiLocker. OTP is confidential and valid for 10 minutes.",
        "Use OTP 455260 to log into your Swiggy account. Do not share the OTP or your number with anyone including Swiggy personnel.",
        "121008 is your OTP to proceed on Probe42. OTPs are SECRET. DO NOT disclose it to anyone."
    ]
    
    # Test Promotional Credit messages
    promotional_messages = [
        "Your happiness keeps us going. So here's Rs.20 Cashback, only for you. Pay using Paytm UPI & avail now. https://p.paytm.me/xCTH/SC20",
        "Hey Vineet! Special Treat of Rs.150 expires tonight! Redeem it with Code: EXTRA150 Shop trending Tops, Bottoms & more Only on Bewakoof: bwkoof.com/4d2",
        "SPECIAL DEAL on flights to New Delhi: Up to Rs.3000 OFF*, Code: RWYVSP. Book now - app.mmyt.co/Xm2V/ozj6c7t8 | Team MakeMyTrip",
        "Dear Customer! Get FLAT 20% OFF on orders over 2500 @ Lenskart! Use code: SM24-386403612LK, Valid till 25-Nov. Shop your favorites now! T&C lskt.me/q8",
        "You have (1) friend request from SNITCH. Accept it with FLAT 25% OFF on Rs 2499 Code: REQUESTED Online Exclusive. https://m.9m.io/SNITCH/mzycsuw",
        "One night to end the year strong! Grab FLAT 20% OFF on Rs 2999 and above! Code: NEWYEAR Online Exclusive https://m.9m.io/SNITCH/oau0gcy -SNITCH",
        "Dear Customer! Rs.101 CREDITED to your Bewakoof Wallet. Redeem to Shop your faves at slashed prices only on Bewakoof bwkoof.com/b1m",
        "ARRIVING NOW: Your next fit from SNITCH! Claim it with FLAT 500 OFF on Rs 2399/- Code: ZIPNOW Expires Soon. https://m.9m.io/SNITCH/avvmywt",
        "Save on buses to Rishikesh with this deal: FLAT 10% OFF* code: MMTBUS. Book Now: app.mmyt.co/Xm2V/1iv0t0q9 - Team MakeMyTrip",
        "READY FOR PICKUP: New fits & FLAT 25% OFF on Rs 2,399 Use code: PICKME25 Online Exclusive ct3.io/5g3Baj3 -SNITCH"
    ]
    
    print("Testing OTP and Promotional Credit classification...")
    print("=" * 80)
    
    # Test OTP messages
    print("\n🔐 TESTING OTP MESSAGES")
    print("-" * 50)
    
    otp_correct = 0
    for i, message in enumerate(otp_messages, 1):
        print(f"\nOTP Test {i}: {message[:60]}{'...' if len(message) > 60 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] == 'Other' and validated_classification['sms_event_subtype'] == 'Non-Financial':
            print("✅ CORRECT: Classified as non-financial")
            otp_correct += 1
        else:
            print("❌ ERROR: Should be non-financial")
    
    # Test Promotional Credit messages
    print(f"\n💰 TESTING PROMOTIONAL CREDIT MESSAGES")
    print("-" * 50)
    
    promo_correct = 0
    for i, message in enumerate(promotional_messages, 1):
        print(f"\nPromo Test {i}: {message[:60]}{'...' if len(message) > 60 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Initial: {initial_classification}")
        print(f"Validated: {validated_classification}")
        
        if validated_classification['sms_type'] == 'Other' and validated_classification['sms_event_subtype'] == 'Non-Financial':
            print("✅ CORRECT: Reclassified as non-financial")
            promo_correct += 1
        else:
            print("❌ ERROR: Should be reclassified as non-financial")
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"OTP Messages: {otp_correct}/{len(otp_messages)} correct ({otp_correct/len(otp_messages)*100:.1f}%)")
    print(f"Promotional Messages: {promo_correct}/{len(promotional_messages)} correct ({promo_correct/len(promotional_messages)*100:.1f}%)")
    print(f"Overall: {otp_correct + promo_correct}/{len(otp_messages) + len(promotional_messages)} correct ({(otp_correct + promo_correct)/(len(otp_messages) + len(promotional_messages))*100:.1f}%)")
    
    if otp_correct == len(otp_messages) and promo_correct == len(promotional_messages):
        print("\n🎉 ALL TESTS PASSED! OTP and Promotional Credit messages are correctly classified as non-financial.")
    else:
        print(f"\n⚠️  Some tests failed. Please review the classification logic.")

if __name__ == "__main__":
    asyncio.run(test_otp_and_promotional_classification())
