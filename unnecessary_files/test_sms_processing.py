#!/usr/bin/env python3
"""
Test SMS Processing with Sample Data

This script creates a sample sms_backup.csv file and demonstrates the SMS processing functionality.
"""

import csv
import os
from sms_backup_processor import process_sms_backup_sync


def create_sample_sms_backup():
    """
    Create a sample sms_backup.csv file with various types of SMS messages.
    """
    sample_sms_data = [
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-001',
            'updateAt': 'Mon, 1 Jan 2024 10:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Rs.500.00 debited from A/c **1234 on 01-01-24 via UPI-PAYTM. Avl Bal Rs.15,000.00. UPI Ref 40*********0.'
        },
        {
            'phoneNumber': 'xx39973810', 
            'id': 'test-002',
            'updateAt': 'Mon, 1 Jan 2024 11:00:00 UTC',
            'senderAddress': 'SBIUPI',
            'text': 'UPI txn of Rs.250.00 to MERCHANT@paytm on 01-01-24. Ref: *********012. If not done by you, call **********.'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-003', 
            'updateAt': 'Mon, 1 Jan 2024 12:00:00 UTC',
            'senderAddress': 'ICICIBANK',
            'text': 'Your EMI of Rs.5,000.00 for Loan A/c ********* has been debited from A/c **4567 on 01-01-24. Outstanding: Rs.50,000.00'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-004',
            'updateAt': 'Mon, 1 Jan 2024 13:00:00 UTC', 
            'senderAddress': 'HDFCBK',
            'text': 'Salary of Rs.75,000.00 credited to A/c **1234 on 01-01-24 from TECH COMPANY PVT LTD. Avl Bal Rs.90,000.00'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-005',
            'updateAt': 'Mon, 1 Jan 2024 14:00:00 UTC',
            'senderAddress': 'SBICARD',
            'text': 'Rs.1,200.00 spent on AMAZON using SBI Card **5678 on 01-01-24. Avl limit Rs.48,800.00'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-006',
            'updateAt': 'Mon, 1 Jan 2024 15:00:00 UTC',
            'senderAddress': 'JIOMNY',
            'text': 'Rs.239.00 recharge successful for Jio number 9876543210. Validity: 28 days. Thanks for choosing Jio!'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-007',
            'updateAt': 'Mon, 1 Jan 2024 16:00:00 UTC',
            'senderAddress': 'NSEIND',
            'text': 'Dear CLIENT123, Traded value Rs.10,000.00 on 01-01-24. Check your registered email for contract note.'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-008',
            'updateAt': 'Mon, 1 Jan 2024 17:00:00 UTC',
            'senderAddress': 'OTPVERIFY',
            'text': 'Your OTP for transaction verification is 123456. Do not share this with anyone. Valid for 10 minutes.'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-009',
            'updateAt': 'Mon, 1 Jan 2024 18:00:00 UTC',
            'senderAddress': 'OFFERS',
            'text': 'SALE! Get 50% off on all electronics. Shop now at www.example.com. Use code SAVE50. Limited time offer!'
        },
        {
            'phoneNumber': 'xx39973810',
            'id': 'test-010',
            'updateAt': 'Mon, 1 Jan 2024 19:00:00 UTC',
            'senderAddress': 'HDFCBK',
            'text': 'Your A/c **1234 balance is Rs.90,000.00 as on 01-01-24 19:00. For mini statement, SMS BAL to 5676712.'
        }
    ]
    
    # Write sample data to CSV
    with open('sms_backup.csv', 'w', newline='', encoding='utf-8') as file:
        fieldnames = ['phoneNumber', 'id', 'updateAt', 'senderAddress', 'text']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(sample_sms_data)
    
    print(f"✅ Created sample sms_backup.csv with {len(sample_sms_data)} SMS messages")
    return len(sample_sms_data)


def main():
    """
    Main function to test SMS processing with sample data.
    """
    print("🧪 SMS Processing Test")
    print("=" * 40)
    
    # Create sample data if sms_backup.csv doesn't exist
    if not os.path.exists('sms_backup.csv'):
        print("📝 Creating sample SMS backup data...")
        create_sample_sms_backup()
    else:
        print("📁 Using existing sms_backup.csv file")
    
    print("\n🔄 Starting SMS processing...")
    
    try:
        # Process the SMS backup
        process_sms_backup_sync('sms_backup.csv', 'test_results.csv')
        
        print("\n✅ Test completed successfully!")
        print("📄 Results saved to: test_results.csv")
        print("📊 Summary saved to: test_results_summary.json")
        
        # Show a preview of the results
        if os.path.exists('test_results.csv'):
            print("\n📋 Preview of results:")
            with open('test_results.csv', 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for i, row in enumerate(reader):
                    if i >= 3:  # Show first 3 rows
                        break
                    print(f"  Row {i+1}: {row['classification']} - {row['sms_type']} - {row['sms_event_subtype']}")
                    if row['amount']:
                        print(f"    Amount: {row['amount']}, Date: {row['date']}")
                    print()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    main()
