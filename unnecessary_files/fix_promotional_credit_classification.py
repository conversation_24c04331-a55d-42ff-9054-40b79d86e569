#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix existing Promotional Credit classifications in the CSV file.
Reclassifies all Promotional Credit messages as non-financial.
"""

import csv
import asyncio
from classifiers import SMSClassifier

async def fix_promotional_credit_classifications():
    """Fix existing Promotional Credit classifications in the CSV file."""
    
    print("Loading existing CSV file...")
    
    # Read the CSV file
    rows = []
    with open('sms_processed_results_fixed.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        for row in reader:
            rows.append(row)
    
    print(f"Total records: {len(rows)}")
    
    # Find Promotional Credit messages
    promotional_credit_messages = []
    for i, row in enumerate(rows):
        if row['sms_event_subtype'] == 'Promotional Credit':
            promotional_credit_messages.append((i, row))
    
    print(f"Promotional Credit messages found: {len(promotional_credit_messages)}")
    
    if len(promotional_credit_messages) == 0:
        print("No Promotional Credit messages to fix!")
        return
    
    classifier = SMSClassifier()
    fixed_count = 0
    fixed_examples = []
    
    print("Processing Promotional Credit messages...")
    
    for idx, (row_idx, row) in enumerate(promotional_credit_messages):
        original_text = row['original_text']
        
        # Re-classify the message
        initial_classification = classifier.classify_sms(original_text)
        
        # Validate classification (this will reclassify Promotional Credit as non-financial)
        validated_classification = classifier.validate_financial_classification(initial_classification, row['amount'])
        
        # Update the row to non-financial
        rows[row_idx]['classification'] = 'non-financial'
        rows[row_idx]['sms_type'] = 'Other'
        rows[row_idx]['sms_event_subtype'] = 'Non-Financial'
        rows[row_idx]['sms_info_type'] = 'Other'
        rows[row_idx]['extracted_data_json'] = '{}'
        rows[row_idx]['amount'] = ''
        rows[row_idx]['date'] = ''
        rows[row_idx]['account_number'] = ''
        rows[row_idx]['bank_name'] = ''
        rows[row_idx]['txn_ref'] = ''
        rows[row_idx]['currency'] = ''
        fixed_count += 1
        
        if len(fixed_examples) < 5:
            fixed_examples.append(original_text)
        
        if fixed_count % 10 == 0:
            print(f"Fixed {fixed_count} messages...")
    
    print(f"Fixed {fixed_count} Promotional Credit messages total")
    
    # Save the corrected CSV
    output_file = 'sms_processed_results_final.csv'
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(rows)
    
    print(f"Saved corrected data to {output_file}")
    
    # Print summary statistics
    financial_count = sum(1 for row in rows if row['classification'] == 'financial')
    non_financial_count = sum(1 for row in rows if row['classification'] == 'non-financial')
    
    print("\nSummary after fixes:")
    print(f"Total records: {len(rows)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    
    # Show some examples of fixed messages
    print("\nExamples of fixed Promotional Credit messages:")
    for text in fixed_examples:
        print(f"- {text[:80]}{'...' if len(text) > 80 else ''}")
    
    # Verify no more Promotional Credit messages exist
    remaining_promo = sum(1 for row in rows if row['sms_event_subtype'] == 'Promotional Credit')
    print(f"\nRemaining Promotional Credit messages: {remaining_promo}")
    
    if remaining_promo == 0:
        print("✅ SUCCESS: All Promotional Credit messages have been reclassified as non-financial!")
    else:
        print("⚠️  WARNING: Some Promotional Credit messages may still exist.")

if __name__ == "__main__":
    asyncio.run(fix_promotional_credit_classifications())
