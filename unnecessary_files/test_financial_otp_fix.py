#!/usr/bin/env python3
"""
Test script to verify that financial OTP and e-mandate messages are correctly classified as financial.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def test_financial_otp_classification():
    """Test financial OTP and e-mandate classification."""
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    # Test financial OTP messages (should be FINANCIAL)
    financial_otp_messages = [
        "412665 is the OTP for Trxn. of INR 2.00 at GOOGLEPLAY with your credit card ending 4465. OTP is valid for 10 mins. Do not share it with anyone - SBI Card",
        "931654 is the OTP for Trxn. of INR 2.00 at GOOGLECLOU with your credit card ending 4465. OTP is valid for 10 mins. Do not share it with anyone - SBI Card",
        "720867 is the OTP for Trxn. of INR 2000.00 at Indian Ins with your credit card ending 4465. OTP is valid for 10 mins. Do not share it with anyone - SBI Card",
        "038846 is the OTP for Trxn. of USD 23.60 at OPENAI with your credit card ending 4465. OTP is valid for 10 mins. Do not share it with anyone - SBI Card",
        "656116 is OTP for online purchase of Rs. 8.20 at ATOM TECHNOLOGIES LT thru State Bank Debit Card 3804. Do not share this OTP with anyone. -SBI"
    ]
    
    # Test e-mandate messages (should be FINANCIAL)
    financial_emandates = [
        "Transaction of Rs.2.00 at GOOGLE PLAY against E-mandate (SiHub ID - Xu8mtKL4za) registered by you at merchant has been debited to your SBI Credit Card ending 4465 on 26-06-25. To manage E-mandate, click: http://www.sbicard.com/emandates",
        "Dear Cardholder, your payment e-Mandate set at merchant platform with your credit card ending 4465 has been registered with the following details. Merchant: Google Play, Description: SCRLPremiumPhotoCollageEditorSCRL, e-Mandate Limit Amount (INR): 249.00, Frequency: weekly, Start date: 26/06/2025, End date:31/12/2035, Si-Hub ID: Xu8mtKL4za. Please note that you have authorised debit of 2.00 from your account towards the first Trxn. against this payment e-Mandate. To manage e-Mandate(s), click: https://www.sbicard.com/emandates - SBI Card",
        "Transaction of Rs.2.00 at GOOGLE CLOUD against E-mandate (SiHub ID - Xu7TbK4Ntl) registered by you at merchant has been debited to your SBI Credit Card ending 4465 on 25-06-25. To manage E-mandate, click: http://www.sbicard.com/emandates",
        "Dear Cardholder, your payment e-Mandate set at merchant platform with your credit card ending 4465 has been registered with the following details. Merchant: Google Cloud, Description: GoogleCloud0144E147890CE630FA, e-Mandate Limit Amount (INR): 75000.00, Frequency: As Presented, Start date: 25/06/2025, End date:31/12/2035, Si-Hub ID: Xu7TbK4Ntl. Please note that you have authorised debit of 2.00 from your account towards the first Trxn. against this payment e-Mandate. To manage e-Mandate(s), click: https://www.sbicard.com/emandates - SBI Card"
    ]
    
    # Test non-financial OTP messages (should be NON-FINANCIAL)
    non_financial_otp_messages = [
        "DO NOT SHARE: Your Google Pay OTP is 904958. Message ID: f6f01vO3FNp",
        "8549 is your CRED verification OTP. The code will be valid for 10 min. Do not share this OTP with anyone.",
        "Use OTP 965945 to log into your Swiggy account. Do not share the OTP or your number with anyone including Swiggy personnel.",
        "000450 is your Amazon OTP. Do not share it with anyone.",
        "248105 is your OTP to access DigiLocker. OTP is confidential and valid for 10 minutes.",
        "851023 is your OTP / verification code for Kite and is valid for 5 minutes. Do not share this with anyone. -Zerodha"
    ]
    
    print("Testing Financial OTP and E-mandate Classification Fix...")
    print("=" * 80)
    
    # Test financial OTP messages
    print("\n💳 TESTING FINANCIAL OTP MESSAGES (Should be FINANCIAL)")
    print("-" * 60)
    
    financial_otp_correct = 0
    for i, message in enumerate(financial_otp_messages, 1):
        print(f"\nFinancial OTP Test {i}: {message[:70]}{'...' if len(message) > 70 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Amount extracted: '{amount}'")
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] != 'Other':
            print("✅ CORRECT: Classified as financial")
            financial_otp_correct += 1
        else:
            print("❌ ERROR: Should be financial")
    
    # Test e-mandate messages
    print(f"\n🔄 TESTING E-MANDATE MESSAGES (Should be FINANCIAL)")
    print("-" * 60)
    
    emandates_correct = 0
    for i, message in enumerate(financial_emandates, 1):
        print(f"\nE-mandate Test {i}: {message[:70]}{'...' if len(message) > 70 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Amount extracted: '{amount}'")
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] != 'Other':
            print("✅ CORRECT: Classified as financial")
            emandates_correct += 1
        else:
            print("❌ ERROR: Should be financial")
    
    # Test non-financial OTP messages
    print(f"\n🔐 TESTING NON-FINANCIAL OTP MESSAGES (Should be NON-FINANCIAL)")
    print("-" * 60)
    
    non_financial_otp_correct = 0
    for i, message in enumerate(non_financial_otp_messages, 1):
        print(f"\nNon-Financial OTP Test {i}: {message[:70]}{'...' if len(message) > 70 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Classification: {validated_classification}")
        
        if validated_classification['sms_type'] == 'Other':
            print("✅ CORRECT: Classified as non-financial")
            non_financial_otp_correct += 1
        else:
            print("❌ ERROR: Should be non-financial")
    
    # Summary
    total_tests = len(financial_otp_messages) + len(financial_emandates) + len(non_financial_otp_messages)
    total_correct = financial_otp_correct + emandates_correct + non_financial_otp_correct
    
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Financial OTP Messages: {financial_otp_correct}/{len(financial_otp_messages)} correct ({financial_otp_correct/len(financial_otp_messages)*100:.1f}%)")
    print(f"E-mandate Messages: {emandates_correct}/{len(financial_emandates)} correct ({emandates_correct/len(financial_emandates)*100:.1f}%)")
    print(f"Non-Financial OTP Messages: {non_financial_otp_correct}/{len(non_financial_otp_messages)} correct ({non_financial_otp_correct/len(non_financial_otp_messages)*100:.1f}%)")
    print(f"Overall: {total_correct}/{total_tests} correct ({total_correct/total_tests*100:.1f}%)")
    
    if total_correct == total_tests:
        print("\n🎉 ALL TESTS PASSED! Financial and non-financial messages are correctly distinguished.")
    else:
        print(f"\n⚠️  {total_tests - total_correct} tests failed. Please review the classification logic.")

if __name__ == "__main__":
    asyncio.run(test_financial_otp_classification())
