#!/usr/bin/env python3
"""
Test script to verify the specific messages mentioned by the user are correctly classified.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor

async def test_specific_messages():
    """Test the specific messages that were wrongly marked as non-financial."""
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    # Messages that should be classified as FINANCIAL
    test_messages = [
        {
            'text': "Transaction of USD23.60 at CHATGPT SUBSCRIPTION against E-mandate (SiHub ID - X3dKeaZgvM) registered by you at merchant has been debited to your SBI Credit Card ending 4465 on 06-05-24. To manage E-mandate, click: http://www.sbicard.com/emandates",
            'expected': 'financial',
            'description': 'E-mandate transaction'
        },
        {
            'text': "Dear SBI Cardholder, outstanding of Rs. 6613.00, on your credit card ending 4465 is due on 05-AUG-24. Min. Amount Due: Rs. 6613.00. Please ignore if already paid. Click here: https://sbicard.com/quickpaynet to make online payment.",
            'expected': 'financial',
            'description': 'Credit card outstanding notification'
        },
        {
            'text': "We have received payment of Rs.37,107.00 via BBPS & the same has been credited to your SBI Credit Card. Your available limit is Rs.148,050.15.",
            'expected': 'financial',
            'description': 'Payment confirmation and credit'
        },
        {
            'text': "Rs. 2907 has been credited to your SBI Credit Card xxxx4465, towards reversal/cashback from IBIBO GROUP PRIVATE LI GURGAON  IN for trxn. dated 07/12/2024",
            'expected': 'financial',
            'description': 'Credit card reversal/cashback'
        },
        {
            'text': "Rs. 1722.2 has been credited to your SBI Credit Card xxxx4465, towards reversal/cashback from IBIBO GROUP PVT LTD MP GURGAON  IN for trxn. dated 07/12/2024",
            'expected': 'financial',
            'description': 'Credit card reversal/cashback'
        },
        {
            'text': "PNR ********** ticket cancelled. Amt 1,325 will be refunded within 3-4 days. Please check your account/instrument from which this tkt was booked.-IRCTC",
            'expected': 'financial',
            'description': 'IRCTC ticket cancellation refund'
        },
        {
            'text': "PNR ********** ticket cancelled. Amt 2,626.08 will be refunded within 3-4 days. Please check your account/instrument from which this tkt was booked.-IRCTC",
            'expected': 'financial',
            'description': 'IRCTC ticket cancellation refund'
        }
    ]
    
    print("Testing Specific Wrong Messages...")
    print("=" * 80)
    
    correct_count = 0
    total_count = len(test_messages)
    
    for i, test_case in enumerate(test_messages, 1):
        message = test_case['text']
        expected = test_case['expected']
        description = test_case['description']
        
        print(f"\nTest {i}: {description}")
        print(f"Message: {message[:80]}{'...' if len(message) > 80 else ''}")
        
        # Initial classification
        initial_classification = classifier.classify_sms(message)
        
        # Extract amount
        amount = await extractor.extract_amount(message)
        
        # Validate classification
        validated_classification = classifier.validate_financial_classification(initial_classification, amount)
        
        print(f"Amount extracted: '{amount}'")
        print(f"Classification: {validated_classification}")
        
        # Check if classification matches expected
        is_financial = validated_classification['sms_type'] != 'Other'
        actual = 'financial' if is_financial else 'non-financial'
        
        if actual == expected:
            print(f"✅ CORRECT: Classified as {actual}")
            correct_count += 1
        else:
            print(f"❌ ERROR: Expected {expected}, got {actual}")
            
            # Show detailed classification for debugging
            print(f"   Initial classification: {initial_classification}")
            print(f"   Validated classification: {validated_classification}")
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Correct: {correct_count}/{total_count} ({correct_count/total_count*100:.1f}%)")
    
    if correct_count == total_count:
        print("\n🎉 ALL TESTS PASSED! All specific messages are now correctly classified.")
    else:
        failed_count = total_count - correct_count
        print(f"\n⚠️  {failed_count} test(s) failed. Please review the classification logic.")
        
        # Show which tests failed
        print("\nFailed tests:")
        for i, test_case in enumerate(test_messages, 1):
            message = test_case['text']
            expected = test_case['expected']
            
            initial_classification = classifier.classify_sms(message)
            amount = await extractor.extract_amount(message)
            validated_classification = classifier.validate_financial_classification(initial_classification, amount)
            
            is_financial = validated_classification['sms_type'] != 'Other'
            actual = 'financial' if is_financial else 'non-financial'
            
            if actual != expected:
                print(f"- Test {i}: {test_case['description']} (expected {expected}, got {actual})")

if __name__ == "__main__":
    asyncio.run(test_specific_messages())
