#!/usr/bin/env python3
"""
Debug script to understand why financial messages are being filtered out.
"""

import asyncio
from classifiers import SMSClassifier
from field_extractors import FieldExtractor
from utils import clean_text

async def debug_financial_filtering():
    """Debug the financial filtering logic."""
    
    # Test messages that should be financial
    test_messages = [
        "Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI",
        "Dear UPI user A/C X4884 debited by 135.46 on date 30Apr24 trf to MCDONALDS Refno ************. If not u? call **********. -SBI",
        "Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank"
    ]
    
    classifier = SMSClassifier()
    extractor = FieldExtractor()
    
    print("DEBUGGING FINANCIAL FILTERING")
    print("="*60)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\nMessage {i}:")
        print(f"Text: {message[:80]}...")
        
        # Step 1: Clean text
        cleaned_text = clean_text(message)
        print(f"Cleaned: {cleaned_text[:80]}...")
        
        # Step 2: Initial classification
        initial_classification = classifier.classify_sms(cleaned_text)
        print(f"Initial classification: {initial_classification}")
        
        # Step 3: Extract amount
        extracted_amount = await extractor.extract_amount(cleaned_text)
        print(f"Extracted amount: '{extracted_amount}'")
        
        # Step 4: Validate classification
        validated_classification = classifier.validate_financial_classification(
            initial_classification, extracted_amount
        )
        print(f"Validated classification: {validated_classification}")
        
        # Step 5: Check basic financial criteria
        basic_financial = (
            validated_classification['sms_type'] != 'Other' and 
            validated_classification['sms_event_subtype'] != 'Non-Financial' and
            extracted_amount and extracted_amount.strip() != ''
        )
        print(f"Basic financial check: {basic_financial}")
        
        # Step 6: Check high-quality financial criteria
        from hybrid_sms_processor import HybridSMSProcessor
        processor = HybridSMSProcessor()
        high_quality = processor._is_high_quality_financial(
            message, validated_classification, extracted_amount
        )
        print(f"High-quality financial check: {high_quality}")
        
        print("-" * 60)

if __name__ == "__main__":
    asyncio.run(debug_financial_filtering())
