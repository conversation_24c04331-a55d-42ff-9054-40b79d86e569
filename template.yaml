AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Globals:
  Function:
    Timeout: 60
    MemorySize: 512
    Runtime: provided.al2023
    Architectures:
      - x86_64
    Environment:
      Variables:
        DB_HOST: !Ref DatabaseHost
        DB_PORT: !Ref DatabasePort
        DB_NAME: !Ref DatabaseName
        DB_USERNAME: !Ref DatabaseUsername
        DB_PASSWORD: !Ref DatabasePassword
        TASK_QUEUE_URL: !Ref TaskQueue
        API_STAGE: !Ref Environment
        BASE_API_URL: !Ref BaseApiUrl

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - staging
      - prod
    Description: Environment name

  BaseApiUrl:
    Type: String
    Default: "https://94gnwh0cr9.execute-api.ap-south-1.amazonaws.com/dev/"
    Description: "Base URL of the API Gateway (post-deploy update recommended)"

  DatabaseHost:
    Type: String
    Default: modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
    Description: Existing PostgreSQL database host/endpoint
    
  DatabasePort:
    Type: String
    Default: "5432"
    Description: PostgreSQL database port

  DatabaseUsername:
    Type: String
    Default: postgres
    NoEcho: true
    Description: PostgreSQL username for existing database

  DatabasePassword:
    Type: String
    Default: "1rpraZEuRTp9aJjzFX47"
    NoEcho: true
    MinLength: 8
    Description: PostgreSQL password for existing database

  DatabaseName:
    Type: String
    Default: "postgres"
    Description: PostgreSQL database name

Resources:
  TaskQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "secure-double-blind-ec-query-tasks-${Environment}"
      VisibilityTimeout: 300
      MessageRetentionPeriod: 1209600

  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  # Lambda functions (no environment override needed, all share Globals)
  CentralStoreFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-store/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/store
            Method: post
            RestApiId: !Ref ApiGateway

  CentralCreateInstitutionFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-create-institution/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/institution
            Method: post
            RestApiId: !Ref ApiGateway

  CompanySubmitFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/submit
            Method: post
            RestApiId: !Ref ApiGateway

  CompanyEncryptFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/encrypt
            Method: post
            RestApiId: !Ref ApiGateway

  CompanyQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/query
            Method: post
            RestApiId: !Ref ApiGateway

  CompanyQueryDetailsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-details/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/query-details
            Method: post
            RestApiId: !Ref ApiGateway

  CompanyAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/query-async
            Method: post
            RestApiId: !Ref ApiGateway
      Policies:
        - SQSSendMessagePolicy:
            QueueName: !GetAtt TaskQueue.QueueName

  CompanyPollFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/poll
            Method: post
            RestApiId: !Ref ApiGateway

  CentralCrossQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-cross-query/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/cross-query
            Method: post
            RestApiId: !Ref ApiGateway

  CentralGetDataFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-get-data/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/get-data
            Method: post
            RestApiId: !Ref ApiGateway

  CentralListInstitutionsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-list-institutions/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/institutions
            Method: get
            RestApiId: !Ref ApiGateway

  CompanyListInsertsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-list-inserts/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/inserts
            Method: get
            RestApiId: !Ref ApiGateway

  CompanyListTaskLogsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-list-task-logs/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/task-logs
            Method: get
            RestApiId: !Ref ApiGateway

  CompanyDecryptFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-decrypt/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/decrypt
            Method: post
            RestApiId: !Ref ApiGateway

  CompanyEditFraudStatusFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-edit-fraud-status/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/edit-fraud-status
            Method: put
            RestApiId: !Ref ApiGateway

  CompanyRevokeFraudStatusFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-revoke-fraud-status/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /institution/{institution_id}/revoke-fraud-status
            Method: delete
            RestApiId: !Ref ApiGateway

  TaskWorkerFunction:
    Type: AWS::Serverless::Function
    DependsOn:
      - TaskQueue
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/task-worker/
      Handler: bootstrap
      Timeout: 300
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt TaskQueue.Arn
            BatchSize: 1
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt TaskQueue.QueueName
        - Statement:
            - Effect: Allow
              Action:
                - execute-api:Invoke
              Resource: "*"

  DatabaseMigrationFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/db-migration/
      Handler: bootstrap
      Timeout: 300

Outputs:
  ApiUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"

  TaskQueueUrl:
    Description: "URL of the SQS queue for async tasks"
    Value: !Ref TaskQueue
    Export:
      Name: !Sub "${AWS::StackName}-TaskQueueUrl"

  TaskQueueArn:
    Description: "ARN of the SQS queue for async tasks"
    Value: !GetAtt TaskQueue.Arn
    Export:
      Name: !Sub "${AWS::StackName}-TaskQueueArn"
