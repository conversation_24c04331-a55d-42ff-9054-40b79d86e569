#!/usr/bin/env python3
"""
SMS Backup Processing Runner

This script processes SMS backup CSV files and generates classification and extraction results.

Usage:
    python run_sms_processing.py                           # Uses sms_backup.csv if available, otherwise SMS-Data.csv
    python run_sms_processing.py input.csv                 # Uses specified input file
    python run_sms_processing.py input.csv output.csv      # Uses specified input and output files

Output CSV Structure:
- classification: financial/non-financial
- sms_type: Purchase, Payment, Deposit & Withdrawal, Accounts, Investment, Other
- sms_event_subtype: UPI, Debit Card, EMI Payment, etc.
- sms_info_type: Outflow, Inflow, Account Status, etc.
- extracted_data_json: All extracted fields as JSON
- Individual fields: amount, date, account_number, bank_name, txn_ref, currency
"""

import os
import sys
import asyncio
from sms_backup_processor import SMSBackupProcessor, process_sms_backup_sync


def find_input_file():
    """
    Find the appropriate input CSV file.
    Priority: sms_backup.csv > SMS-Data.csv
    """
    if os.path.exists('sms_backup.csv'):
        return 'sms_backup.csv'
    elif os.path.exists('SMS-Data.csv'):
        return 'SMS-Data.csv'
    else:
        return None


def main():
    """
    Main function to run SMS processing with command line argument support.
    """
    print("🔍 SMS Backup Processor")
    print("=" * 50)
    
    # Parse command line arguments
    if len(sys.argv) == 1:
        # No arguments - auto-detect input file
        input_file = find_input_file()
        if not input_file:
            print("❌ No input CSV file found!")
            print("   Please ensure either 'sms_backup.csv' or 'SMS-Data.csv' exists in the current directory.")
            return
        output_file = 'sms_processed_results.csv'
        
    elif len(sys.argv) == 2:
        # Input file specified
        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            print(f"❌ Input file '{input_file}' not found!")
            return
        output_file = 'sms_processed_results.csv'
        
    elif len(sys.argv) == 3:
        # Both input and output files specified
        input_file = sys.argv[1]
        output_file = sys.argv[2]
        if not os.path.exists(input_file):
            print(f"❌ Input file '{input_file}' not found!")
            return
            
    else:
        print("❌ Too many arguments!")
        print("Usage: python run_sms_processing.py [input_file] [output_file]")
        return
    
    print(f"📥 Input file: {input_file}")
    print(f"📤 Output file: {output_file}")
    print()
    
    # Check if output file already exists
    if os.path.exists(output_file):
        response = input(f"⚠️  Output file '{output_file}' already exists. Overwrite? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Processing cancelled.")
            return
    
    try:
        # Run the processing
        process_sms_backup_sync(input_file, output_file)
        
        print(f"\n🎉 Processing completed successfully!")
        print(f"📄 Results saved to: {output_file}")
        print(f"📊 Summary saved to: {output_file.replace('.csv', '_summary.json')}")
        
        # Show file sizes
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📏 Output file size: {file_size:,} bytes")
            
    except KeyboardInterrupt:
        print("\n❌ Processing interrupted by user.")
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        print("Please check the input file format and try again.")


if __name__ == "__main__":
    main()
