#!/usr/bin/env python3
"""
Complete hybrid SMS processing script with improved financial filtering.
Processes the entire SMS backup dataset with high-quality financial message filtering.
"""

import asyncio
import time
import os
from process_full_sms_dataset import FullSMSProcessor

async def main():
    """Main function to run complete hybrid processing."""
    
    # Configuration
    INPUT_FILE = "sms_backup.csv"
    OUTPUT_FILE = "hybrid_sms_processed_results_complete.csv"
    BATCH_SIZE = 50  # Process in batches to manage memory and API limits
    
    # OpenAI API key
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    
    print("COMPLETE HYBRID SMS PROCESSING")
    print("="*70)
    print("🔄 Processing entire SMS dataset with improved filtering")
    print("="*70)
    
    # Check input file
    if not os.path.exists(INPUT_FILE):
        print(f"❌ Error: Input file '{INPUT_FILE}' not found!")
        return
    
    # Get file size
    with open(INPUT_FILE, 'r', encoding='utf-8') as f:
        total_lines = sum(1 for line in f) - 1  # Subtract header
    
    print(f"📄 Input file: {INPUT_FILE}")
    print(f"📊 Total messages: {total_lines:,}")
    print(f"📤 Output file: {OUTPUT_FILE}")
    print(f"🔧 Batch size: {BATCH_SIZE}")
    print()
    
    print("🎯 PROCESSING APPROACH:")
    print("✅ Rule-based classification (financial vs non-financial)")
    print("✅ Amount extraction validation")
    print("✅ High-quality financial message filtering")
    print("✅ OpenAI extraction ONLY for verified financial messages")
    print("✅ Cost-effective processing (estimated 30-40% OpenAI usage)")
    print()
    
    # Estimates based on test results
    estimated_financial_rate = 0.35  # 35% from test
    estimated_openai_calls = total_lines * estimated_financial_rate
    estimated_cost = estimated_openai_calls * 0.002
    estimated_time_minutes = (total_lines / 100) * 1.2  # Based on test rate
    
    print("📈 ESTIMATES:")
    print(f"   Financial messages: ~{estimated_openai_calls:.0f} ({estimated_financial_rate*100:.0f}%)")
    print(f"   OpenAI API calls: ~{estimated_openai_calls:.0f}")
    print(f"   Estimated cost: ~${estimated_cost:.2f}")
    print(f"   Estimated time: ~{estimated_time_minutes:.1f} minutes")
    print()
    
    # Confirm processing
    print("⚠️  IMPORTANT:")
    print("   - This will process ALL SMS messages")
    print("   - OpenAI API calls will be made for financial messages")
    print("   - Processing cannot be easily resumed if interrupted")
    print("   - Results will be saved to CSV file")
    print()
    
    response = input("🤔 Continue with complete processing? (y/n): ").strip().lower()
    if response != 'y':
        print("❌ Processing cancelled.")
        return
    
    # Initialize processor
    print("\n🚀 Starting complete hybrid processing...")
    processor = FullSMSProcessor(OPENAI_API_KEY, BATCH_SIZE)
    
    # Process dataset
    try:
        start_time = time.time()
        results = await processor.process_full_dataset(INPUT_FILE, OUTPUT_FILE)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        print(f"\n🎉 PROCESSING COMPLETED SUCCESSFULLY!")
        print("="*70)
        print(f"⏱️  Total time: {total_time/60:.1f} minutes")
        print(f"📊 Messages processed: {len(results):,}")
        print(f"💳 Financial messages: {processor.stats['financial_count']:,}")
        print(f"📢 Non-financial messages: {processor.stats['non_financial_count']:,}")
        print(f"❌ Errors: {processor.stats['error_count']:,}")
        print(f"🤖 OpenAI calls: {processor.stats['openai_calls']:,}")
        print(f"💰 Estimated cost: ${processor.stats['openai_calls'] * 0.002:.2f}")
        print(f"📄 Results saved to: {OUTPUT_FILE}")
        print("="*70)
        
        # Calculate efficiency metrics
        if len(results) > 0:
            financial_rate = processor.stats['financial_count'] / len(results) * 100
            openai_efficiency = processor.stats['openai_calls'] / len(results) * 100
            error_rate = processor.stats['error_count'] / len(results) * 100
            
            print(f"\n📈 EFFICIENCY METRICS:")
            print(f"   Financial rate: {financial_rate:.1f}%")
            print(f"   OpenAI efficiency: {openai_efficiency:.1f}% (only financial messages)")
            print(f"   Error rate: {error_rate:.1f}%")
            print(f"   Processing rate: {len(results)/total_time:.1f} messages/second")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Review results in '{OUTPUT_FILE}'")
        print(f"   2. Run analysis: python generate_processing_summary.py")
        print(f"   3. Compare with original: python compare_results.py")
        
    except KeyboardInterrupt:
        print("\n⚠️  Processing interrupted by user")
        print("   Partial results may be available")
    except Exception as e:
        print(f"\n❌ Processing failed: {e}")
        print("   Please check the error and try again")

if __name__ == "__main__":
    asyncio.run(main())
