#!/bin/bash

# Test script for the company-query-details API
# This script demonstrates how to retrieve query details and display the encryption table

# Configuration
API_BASE_URL="https://94gnwh0cr9.execute-api.ap-south-1.amazonaws.com/dev"
INSTITUTION_ID="1"  # Replace with actual institution ID

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Company Query Details API Test ===${NC}"
echo

# Function to display the encryption table
display_encryption_table() {
    local json_data="$1"
    
    echo -e "${YELLOW}Encryption Table:${NC}"
    echo "┌─────────────────┬─────────────────┬─────────────────┬─────────────────┬─────────────────┬─────────────────┐"
    echo "│ Query ID        │ Encrypted Query │ Intermediate    │ Encrypted by    │ Consortium      │ Company ID      │"
    echo "│ (Task ID)       │ (A_Bq)          │ (A_Bq_Bn)       │ Partner (A_Bn)  │ (A_Bn)          │                 │"
    echo "├─────────────────┼─────────────────┼─────────────────┼─────────────────┼─────────────────┼─────────────────┤"
    
    # Extract encryption table data using jq
    echo "$json_data" | jq -r '.data.encryption_table[] | 
        "│ " + (.query_id | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + 
        " │ " + (.encrypted_query | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + 
        " │ " + (.intermediate | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + 
        " │ " + (.encrypted_by_partner | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + 
        " │ " + (.consortium | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + 
        " │ " + (.company_id | .[0:15] + (if length > 15 then "..." else "" end) | lpad(15)) + " │"'
    
    echo "└─────────────────┴─────────────────┴─────────────────┴─────────────────┴─────────────────┴─────────────────┘"
}

# Function to display query summary
display_query_summary() {
    local json_data="$1"
    
    echo -e "${YELLOW}Query Summary:${NC}"
    echo "Task ID: $(echo "$json_data" | jq -r '.data.task_id')"
    echo "Query ID: $(echo "$json_data" | jq -r '.data.query_id')"
    echo "Institution: $(echo "$json_data" | jq -r '.data.institution_name')"
    echo "Status: $(echo "$json_data" | jq -r '.data.status')"
    echo "Response Time: $(echo "$json_data" | jq -r '.data.response_time_ms')ms"
    echo "Created: $(echo "$json_data" | jq -r '.data.created_at')"
    echo
}

# Function to display original identifiers
display_original_identifiers() {
    local json_data="$1"
    
    echo -e "${YELLOW}Original Identifiers:${NC}"
    echo "$json_data" | jq -r '.data.original_identifiers[] | 
        "Type: \(.type), Identifier: \(.identifier)"'
    echo
}

# Function to display fraud data results
display_fraud_data() {
    local json_data="$1"
    
    echo -e "${YELLOW}Fraud Data Results:${NC}"
    local fraud_count=$(echo "$json_data" | jq '.data.fraud_data_results | length')
    echo "Total fraud records found: $fraud_count"
    
    if [ "$fraud_count" -gt 0 ]; then
        echo "$json_data" | jq -r '.data.fraud_data_results[] | 
            "ID: \(.id), Status: \(.status), Type: \(.fraud_type), Created: \(.created_at)"'
    fi
    echo
}

# Test 1: Get query details by task ID
echo -e "${GREEN}Test 1: Get query details by task ID${NC}"
echo "Enter a task ID to retrieve details:"
read -p "Task ID: " TASK_ID

if [ -n "$TASK_ID" ]; then
    echo "Retrieving query details for task ID: $TASK_ID"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"task_id\": \"$TASK_ID\"}" \
        "$API_BASE_URL/institution/$INSTITUTION_ID/query-details")
    
    if echo "$response" | jq -e '.success' > /dev/null; then
        echo -e "${GREEN}✓ Successfully retrieved query details${NC}"
        echo
        
        display_query_summary "$response"
        display_original_identifiers "$response"
        display_encryption_table "$response"
        display_fraud_data "$response"
    else
        echo -e "${RED}✗ Failed to retrieve query details${NC}"
        echo "Error: $(echo "$response" | jq -r '.message')"
    fi
fi

echo

# Test 2: Get query details by query ID
echo -e "${GREEN}Test 2: Get query details by query ID${NC}"
echo "Enter a query ID to retrieve details:"
read -p "Query ID: " QUERY_ID

if [ -n "$QUERY_ID" ]; then
    echo "Retrieving query details for query ID: $QUERY_ID"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"query_id\": \"$QUERY_ID\"}" \
        "$API_BASE_URL/institution/$INSTITUTION_ID/query-details")
    
    if echo "$response" | jq -e '.success' > /dev/null; then
        echo -e "${GREEN}✓ Successfully retrieved query details${NC}"
        echo
        
        display_query_summary "$response"
        display_original_identifiers "$response"
        display_encryption_table "$response"
        display_fraud_data "$response"
    else
        echo -e "${RED}✗ Failed to retrieve query details${NC}"
        echo "Error: $(echo "$response" | jq -r '.message')"
    fi
fi

echo
echo -e "${BLUE}=== Test Complete ===${NC}" 