#!/usr/bin/env python3
"""
Generate comprehensive summary and analysis of hybrid SMS processing results.
"""

import csv
import json
from collections import defaultdict, Counter
from datetime import datetime

def load_csv_data(filename):
    """Load CSV data into a list of dictionaries."""
    data = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        print(f"✅ Loaded {len(data)} records from {filename}")
    except FileNotFoundError:
        print(f"❌ File {filename} not found.")
        return []
    except Exception as e:
        print(f"❌ Error loading {filename}: {e}")
        return []
    return data

def analyze_classifications(data):
    """Analyze classification results."""
    print("\n" + "="*60)
    print("CLASSIFICATION ANALYSIS")
    print("="*60)
    
    total = len(data)
    if total == 0:
        print("No data to analyze")
        return
    
    # Basic classification stats
    classifications = Counter(row.get('classification', 'unknown') for row in data)
    
    print(f"Total messages: {total:,}")
    for classification, count in classifications.most_common():
        percentage = (count / total) * 100
        print(f"  {classification.title()}: {count:,} ({percentage:.1f}%)")
    
    # SMS type breakdown for financial messages
    financial_data = [row for row in data if row.get('classification') == 'financial']
    if financial_data:
        print(f"\nFinancial Message Types:")
        sms_types = Counter(row.get('sms_type', 'unknown') for row in financial_data)
        for sms_type, count in sms_types.most_common():
            percentage = (count / len(financial_data)) * 100
            print(f"  {sms_type}: {count:,} ({percentage:.1f}%)")
    
    # Error analysis
    error_data = [row for row in data if row.get('processing_status') == 'error']
    if error_data:
        print(f"\nError Analysis:")
        print(f"  Total errors: {len(error_data)}")
        error_types = Counter(row.get('error_message', 'unknown')[:50] for row in error_data)
        for error_type, count in error_types.most_common(5):
            print(f"  {error_type}...: {count}")

def analyze_field_extraction(data):
    """Analyze field extraction quality."""
    print("\n" + "="*60)
    print("FIELD EXTRACTION ANALYSIS")
    print("="*60)
    
    financial_data = [row for row in data if row.get('classification') == 'financial']
    if not financial_data:
        print("No financial messages to analyze")
        return
    
    total_financial = len(financial_data)
    print(f"Analyzing {total_financial:,} financial messages")
    
    # Key field extraction rates
    key_fields = ['amount', 'entity_name', 'account_number', 'txn_ref', 'currency']
    
    print(f"\nField Extraction Rates:")
    for field in key_fields:
        extracted_count = sum(1 for row in financial_data 
                            if row.get(field, '').strip() != '')
        rate = (extracted_count / total_financial) * 100
        print(f"  {field}: {extracted_count:,}/{total_financial:,} ({rate:.1f}%)")
    
    # OpenAI vs Rule-based extraction comparison
    openai_extractions = 0
    rich_extractions = 0
    
    for row in financial_data:
        extracted_data = row.get('extracted_data_json', '{}')
        if extracted_data != '{}':
            try:
                data_dict = json.loads(extracted_data)
                if len(data_dict) > 5:  # Rich extraction indicates OpenAI
                    openai_extractions += 1
                    # Count non-null fields
                    non_null_fields = sum(1 for v in data_dict.values() if v is not None and str(v).strip() != '')
                    if non_null_fields >= 3:
                        rich_extractions += 1
            except:
                pass
    
    print(f"\nOpenAI Extraction Analysis:")
    print(f"  Messages with OpenAI extraction: {openai_extractions:,} ({openai_extractions/total_financial*100:.1f}%)")
    print(f"  Messages with rich extraction: {rich_extractions:,} ({rich_extractions/total_financial*100:.1f}%)")

def analyze_sender_patterns(data):
    """Analyze sender address patterns."""
    print("\n" + "="*60)
    print("SENDER PATTERN ANALYSIS")
    print("="*60)
    
    # Top senders overall
    senders = Counter(row.get('sender_address', 'unknown') for row in data)
    print(f"Top 10 Senders (Overall):")
    for sender, count in senders.most_common(10):
        percentage = (count / len(data)) * 100
        print(f"  {sender}: {count:,} ({percentage:.1f}%)")
    
    # Top senders for financial messages
    financial_data = [row for row in data if row.get('classification') == 'financial']
    if financial_data:
        financial_senders = Counter(row.get('sender_address', 'unknown') for row in financial_data)
        print(f"\nTop 10 Senders (Financial Messages):")
        for sender, count in financial_senders.most_common(10):
            percentage = (count / len(financial_data)) * 100
            print(f"  {sender}: {count:,} ({percentage:.1f}%)")

def analyze_amounts(data):
    """Analyze transaction amounts."""
    print("\n" + "="*60)
    print("AMOUNT ANALYSIS")
    print("="*60)
    
    financial_data = [row for row in data if row.get('classification') == 'financial']
    amounts = []
    
    for row in financial_data:
        amount_str = row.get('amount', '').strip()
        if amount_str:
            try:
                # Clean amount string
                amount_clean = amount_str.replace(',', '').replace('Rs.', '').replace('₹', '').strip()
                amount = float(amount_clean)
                amounts.append(amount)
            except:
                pass
    
    if amounts:
        amounts.sort()
        total_amount = sum(amounts)
        avg_amount = total_amount / len(amounts)
        median_amount = amounts[len(amounts)//2]
        
        print(f"Amount Statistics:")
        print(f"  Messages with amounts: {len(amounts):,}/{len(financial_data):,} ({len(amounts)/len(financial_data)*100:.1f}%)")
        print(f"  Total transaction value: ₹{total_amount:,.2f}")
        print(f"  Average amount: ₹{avg_amount:,.2f}")
        print(f"  Median amount: ₹{median_amount:,.2f}")
        print(f"  Min amount: ₹{amounts[0]:,.2f}")
        print(f"  Max amount: ₹{amounts[-1]:,.2f}")
        
        # Amount ranges
        ranges = [
            (0, 100, "₹0-100"),
            (100, 500, "₹100-500"),
            (500, 1000, "₹500-1K"),
            (1000, 5000, "₹1K-5K"),
            (5000, 10000, "₹5K-10K"),
            (10000, float('inf'), "₹10K+")
        ]
        
        print(f"\nAmount Distribution:")
        for min_amt, max_amt, label in ranges:
            count = sum(1 for amt in amounts if min_amt <= amt < max_amt)
            if count > 0:
                percentage = (count / len(amounts)) * 100
                print(f"  {label}: {count:,} ({percentage:.1f}%)")

def generate_sample_extractions(data, num_samples=10):
    """Generate sample extractions to show quality."""
    print("\n" + "="*60)
    print("SAMPLE EXTRACTIONS")
    print("="*60)
    
    financial_data = [row for row in data if row.get('classification') == 'financial']
    
    # Get samples with good extractions
    good_samples = []
    for row in financial_data:
        if (row.get('amount', '').strip() and 
            row.get('entity_name', '').strip() and
            len(good_samples) < num_samples):
            good_samples.append(row)
    
    for i, sample in enumerate(good_samples, 1):
        print(f"\nSample {i}:")
        print(f"  Text: {sample.get('original_text', '')[:80]}...")
        print(f"  Sender: {sample.get('sender_address', 'N/A')}")
        print(f"  Type: {sample.get('sms_type', 'N/A')} / {sample.get('sms_event_subtype', 'N/A')}")
        print(f"  Amount: {sample.get('amount', 'N/A')}")
        print(f"  Entity: {sample.get('entity_name', 'N/A')}")
        print(f"  Account: {sample.get('account_number', 'N/A')}")
        print(f"  Ref: {sample.get('txn_ref', 'N/A')}")

def compare_with_original(hybrid_file, original_file):
    """Compare hybrid results with original results."""
    print("\n" + "="*60)
    print("COMPARISON WITH ORIGINAL RESULTS")
    print("="*60)
    
    original_data = load_csv_data(original_file)
    if not original_data:
        print("Original results not available for comparison")
        return
    
    hybrid_data = load_csv_data(hybrid_file)
    if not hybrid_data:
        print("Hybrid results not available")
        return
    
    # Create lookups
    original_lookup = {row['original_id']: row for row in original_data}
    hybrid_lookup = {row['original_id']: row for row in hybrid_data}
    
    common_ids = set(original_lookup.keys()) & set(hybrid_lookup.keys())
    print(f"Common messages: {len(common_ids):,}")
    
    # Classification changes
    classification_changes = defaultdict(int)
    for msg_id in common_ids:
        orig_class = original_lookup[msg_id].get('classification', 'unknown')
        hybrid_class = hybrid_lookup[msg_id].get('classification', 'unknown')
        if orig_class != hybrid_class:
            classification_changes[f"{orig_class} → {hybrid_class}"] += 1
    
    if classification_changes:
        print(f"\nClassification Changes:")
        for change, count in classification_changes.items():
            print(f"  {change}: {count:,}")
    else:
        print(f"\nNo classification changes detected")

def main():
    """Main function to generate comprehensive summary."""
    
    HYBRID_FILE = "hybrid_sms_processed_results_complete.csv"
    ORIGINAL_FILE = "sms_processed_results.csv"
    
    print("SMS PROCESSING RESULTS ANALYSIS")
    print("="*60)
    print(f"Analyzing: {HYBRID_FILE}")
    
    # Load hybrid results
    hybrid_data = load_csv_data(HYBRID_FILE)
    if not hybrid_data:
        print("No hybrid results found. Please run processing first.")
        return
    
    # Run all analyses
    analyze_classifications(hybrid_data)
    analyze_field_extraction(hybrid_data)
    analyze_sender_patterns(hybrid_data)
    analyze_amounts(hybrid_data)
    generate_sample_extractions(hybrid_data)
    compare_with_original(HYBRID_FILE, ORIGINAL_FILE)
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    print(f"📊 Full analysis of {len(hybrid_data):,} messages completed")
    print(f"📄 Results file: {HYBRID_FILE}")

if __name__ == "__main__":
    main()
