# API Documentation & Postman Collection Export

This directory contains tools to export Postman collections from the OpenAPI specifications defined in the CloudFormation template.

## Features Added

✅ **Full OpenAPI 3.0 Integration** - All API Gateways now include comprehensive OpenAPI specifications
✅ **Swagger UI Documentation** - Live, interactive API documentation
✅ **Postman Collection Export** - Automated conversion from OpenAPI to Postman collections
✅ **Environment Configuration** - Pre-configured Postman environments with sample data
✅ **Sample Request/Response Examples** - Complete with realistic test data

## Quick Start

### 1. Deploy Your Stack
```bash
sam build && sam deploy --guided
```

### 2. Export Postman Collections
```bash
cd scripts
npm install
node export-postman.js
```

### 3. Access Documentation
After deployment, you'll have access to:

- **Swagger UI Documentation**: Available at `/swagger` endpoint for each API
- **OpenAPI JSON**: Available at `/v1/swagger.json` for each API
- **Postman Collections**: Auto-generated from OpenAPI specs

## What's Included

### API Documentation URLs
After deployment, access live documentation at:
- Central Server: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/swagger`
- Company A: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/swagger`
- Company B: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/swagger`
- Company C: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/swagger`

### OpenAPI Specifications
Raw OpenAPI 3.0 JSON specs available at:
- Central Server: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/v1/swagger.json`
- Company A: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/v1/swagger.json`
- Company B: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/v1/swagger.json`
- Company C: `https://{api-id}.execute-api.{region}.amazonaws.com/{stage}/v1/swagger.json`

## Sample API Requests

### Central Server - Store Fraud Data
```json
POST /central/store
{
  "company_id": "company_a",
  "encrypted_data": {
    "phone": "04a1b2c3d4e5f6789...",
    "email": "048f7e6d5c4b3a21...",
    "account": "041a2b3c4d5e6f78..."
  },
  "timestamp": "2025-07-21T10:30:00Z"
}
```

### Company A - Submit Fraud Data
```json
POST /company-a/submit
{
  "phone": "+**********",
  "email": "<EMAIL>",
  "account_number": "**********",
  "incident_type": "credit_card_fraud",
  "description": "Suspicious transaction pattern"
}
```

### Company A - Query for Matches
```json
POST /company-a/query
{
  "identifiers": {
    "phone": "+**********",
    "email": "<EMAIL>"
  },
  "target_companies": ["company_b", "company_c"]
}
```

## Export Script Features

The export script (`export-postman.js`) provides:

- **Automatic Discovery**: Reads CloudFormation outputs to find API URLs
- **OpenAPI Download**: Fetches live OpenAPI specifications
- **Collection Generation**: Converts OpenAPI to Postman collections
- **Environment Setup**: Creates environment with all necessary variables
- **Sample Data**: Includes realistic test data for all endpoints
- **Documentation**: Generates comprehensive README with usage examples

## Directory Structure After Export

```
postman-collections/
├── central-server-api.postman_collection.json
├── company-a-api.postman_collection.json
├── company-b-api.postman_collection.json
├── company-c-api.postman_collection.json
├── postman-environment.json
└── README.md
```

## Error Response Format

All APIs use a standard error format:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Missing required field: phone",
    "details": "The phone field is required for fraud detection queries"
  }
}
```

## Common Error Codes
- `INVALID_REQUEST`: Malformed or missing required fields
- `ENCRYPTION_ERROR`: Failed to encrypt/decrypt data
- `DATABASE_ERROR`: Database operation failed
- `COMPANY_NOT_FOUND`: Specified company does not exist
- `QUERY_NOT_FOUND`: Query ID not found or expired
- `RATE_LIMIT_EXCEEDED`: Too many requests from client

## Prerequisites

- Node.js 16+ (for export script)
- AWS CLI configured
- Deployed CloudFormation stack
- Postman (for importing collections)

## Troubleshooting

### Export Script Issues
1. **AWS CLI not configured**: Run `aws configure`
2. **Stack not found**: Verify stack name and region
3. **Node dependencies**: Run `npm install` in scripts directory

### API Documentation Issues
1. **Swagger UI not loading**: Check API Gateway deployment
2. **CORS errors**: Verify CORS configuration in template
3. **Authentication**: Ensure API key is properly configured

## Support

For issues or questions:
1. Check CloudFormation stack outputs for correct URLs
2. Verify AWS credentials and permissions
3. Review API Gateway logs for debugging
