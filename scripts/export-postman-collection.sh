#!/bin/bash

# Export Postman Collections from OpenAPI Specifications
# This script converts OpenAPI/Swagger specs to Postman collections

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Exporting Postman Collections from OpenAPI Specifications${NC}"
echo "=============================================================="

# Check if required tools are installed
check_dependencies() {
    echo -e "${YELLOW}📋 Checking dependencies...${NC}"
    
    # Check for openapi2postman-cli (install if not present)
    if ! command -v openapi2postmancli &> /dev/null; then
        echo -e "${YELLOW}⚙️  Installing openapi-to-postman CLI tool...${NC}"
        npm install -g openapi-to-postmancollection-v2
    fi
    
    # Check for jq
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}❌ jq is required but not installed. Please install jq first.${NC}"
        echo "   On macOS: brew install jq"
        echo "   On Ubuntu: sudo apt-get install jq"
        exit 1
    fi
    
    # Check for curl
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl is required but not installed.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All dependencies are available${NC}"
}

# Function to get stack outputs
get_stack_outputs() {
    local stack_name="${1:-secure-double-blind-ec-dev}"
    echo -e "${YELLOW}📤 Getting stack outputs for: ${stack_name}${NC}"
    
    aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].Outputs' \
        --output json > stack-outputs.json
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to get stack outputs. Make sure the stack is deployed and you have AWS credentials configured.${NC}"
        exit 1
    fi
}

# Function to extract URL from stack outputs
get_output_value() {
    local output_key="$1"
    jq -r ".[] | select(.OutputKey == \"$output_key\") | .OutputValue" stack-outputs.json
}

# Function to convert OpenAPI to Postman collection
convert_to_postman() {
    local api_name="$1"
    local openapi_url="$2"
    local output_file="$3"
    
    echo -e "${YELLOW}🔄 Converting ${api_name} OpenAPI spec to Postman collection...${NC}"
    
    # Download OpenAPI spec
    local temp_openapi_file="temp_${api_name}_openapi.json"
    curl -s "$openapi_url" > "$temp_openapi_file"
    
    if [ $? -ne 0 ] || [ ! -s "$temp_openapi_file" ]; then
        echo -e "${RED}❌ Failed to download OpenAPI spec from: $openapi_url${NC}"
        rm -f "$temp_openapi_file"
        return 1
    fi
    
    # Convert to Postman collection using node.js script
    node -e "
        const converter = require('openapi-to-postmancollection');
        const fs = require('fs');
        
        const openApiSpec = JSON.parse(fs.readFileSync('$temp_openapi_file', 'utf8'));
        
        converter.convert({ type: 'json', data: openApiSpec }, {}, (err, conversionResult) => {
            if (!conversionResult.result) {
                console.error('Conversion failed:', err);
                process.exit(1);
            }
            
            const collection = conversionResult.output[0].data;
            
            // Add authentication and environment variables
            collection.auth = {
                type: 'apikey',
                apikey: [
                    { key: 'key', value: 'x-api-key', type: 'string' },
                    { key: 'value', value: '{{api_key}}', type: 'string' },
                    { key: 'in', value: 'header', type: 'string' }
                ]
            };
            
            // Add variables
            collection.variable = [
                { key: 'base_url', value: '{{base_url}}', type: 'string' },
                { key: 'api_key', value: '{{api_key}}', type: 'string' },
                { key: 'company_id', value: 'company_a', type: 'string' },
                { key: 'environment', value: 'dev', type: 'string' }
            ];
            
            fs.writeFileSync('$output_file', JSON.stringify(collection, null, 2));
            console.log('✅ Conversion successful: $output_file');
        });
    "
    
    # Clean up temp file
    rm -f "$temp_openapi_file"
}

# Function to create Postman environment file
create_postman_environment() {
    local stack_name="$1"
    local env_file="postman-environment.json"
    
    echo -e "${YELLOW}🌍 Creating Postman environment file...${NC}"
    
    # Get API URLs from stack outputs
    local central_api_url=$(get_output_value "CentralServerApiUrl")
    local company_a_api_url=$(get_output_value "CompanyAServerApiUrl")
    local company_b_api_url=$(get_output_value "CompanyBServerApiUrl")
    local company_c_api_url=$(get_output_value "CompanyCServerApiUrl")
    
    cat > "$env_file" << EOF
{
    "id": "$(uuidgen | tr '[:upper:]' '[:lower:]')",
    "name": "Secure Double Blind EC - ${stack_name}",
    "values": [
        {
            "key": "central_server_url",
            "value": "${central_api_url}",
            "enabled": true
        },
        {
            "key": "company_a_url",
            "value": "${company_a_api_url}",
            "enabled": true
        },
        {
            "key": "company_b_url",
            "value": "${company_b_api_url}",
            "enabled": true
        },
        {
            "key": "company_c_url",
            "value": "${company_c_api_url}",
            "enabled": true
        },
        {
            "key": "api_key",
            "value": "your-api-key-here",
            "enabled": true
        },
        {
            "key": "environment",
            "value": "dev",
            "enabled": true
        }
    ],
    "_postman_variable_scope": "environment",
    "_postman_exported_at": "$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")",
    "_postman_exported_using": "Export Script v1.0.0"
}
EOF
    
    echo -e "${GREEN}✅ Created Postman environment: $env_file${NC}"
}

# Main execution
main() {
    local stack_name="${1:-secure-double-blind-ec-dev}"
    local output_dir="postman-collections"
    
    echo -e "${BLUE}Stack Name: $stack_name${NC}"
    echo -e "${BLUE}Output Directory: $output_dir${NC}"
    echo ""
    
    # Create output directory
    mkdir -p "$output_dir"
    cd "$output_dir"
    
    # Check dependencies
    check_dependencies
    
    # Get stack outputs
    get_stack_outputs "$stack_name"
    
    # Get OpenAPI URLs
    local central_openapi_url=$(get_output_value "CentralServerOpenApiUrl")
    local company_a_openapi_url=$(get_output_value "CompanyAServerOpenApiUrl")
    local company_b_openapi_url=$(get_output_value "CompanyBServerOpenApiUrl")
    local company_c_openapi_url=$(get_output_value "CompanyCServerOpenApiUrl")
    
    echo -e "${YELLOW}🔗 OpenAPI URLs:${NC}"
    echo "   Central Server: $central_openapi_url"
    echo "   Company A: $company_a_openapi_url"
    echo "   Company B: $company_b_openapi_url"
    echo "   Company C: $company_c_openapi_url"
    echo ""
    
    # Convert each API to Postman collection
    convert_to_postman "central-server" "$central_openapi_url" "central-server-api.postman_collection.json"
    convert_to_postman "company-a" "$company_a_openapi_url" "company-a-api.postman_collection.json"
    convert_to_postman "company-b" "$company_b_openapi_url" "company-b-api.postman_collection.json"
    convert_to_postman "company-c" "$company_c_openapi_url" "company-c-api.postman_collection.json"
    
    # Create Postman environment
    create_postman_environment "$stack_name"
    
    # Clean up
    rm -f stack-outputs.json
    
    echo ""
    echo -e "${GREEN}🎉 Export completed successfully!${NC}"
    echo -e "${GREEN}📁 Files created in: $(pwd)${NC}"
    echo ""
    echo -e "${YELLOW}📋 Import Instructions:${NC}"
    echo "   1. Open Postman"
    echo "   2. Click 'Import' button"
    echo "   3. Import all .postman_collection.json files"
    echo "   4. Import postman-environment.json as environment"
    echo "   5. Select the imported environment"
    echo "   6. Update the 'api_key' variable with your actual API key"
    echo ""
    echo -e "${YELLOW}🔗 Swagger Documentation URLs:${NC}"
    echo "   Central Server: $(get_output_value "CentralServerSwaggerUrl")"
    echo "   Company A: $(get_output_value "CompanyAServerSwaggerUrl")"
    echo "   Company B: $(get_output_value "CompanyBServerSwaggerUrl")"
    echo "   Company C: $(get_output_value "CompanyCServerSwaggerUrl")"
}

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
