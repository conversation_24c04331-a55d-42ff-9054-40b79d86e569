#!/bin/bash

# Map of Lambda directories to function names
declare -A lambdas=(
    ["central-cross-query"]="CentralCrossQueryFunction"
    ["central-get-data"]="CentralGetDataFunction"
    ["company-decrypt"]="CompanyDecryptFunction"
    ["db-migration"]="DatabaseMigrationFunction"
)

# Map of shared Lambda directories to multiple function names
declare -A shared_lambdas=(
    ["company-encrypt"]="CompanyAEncryptFunction CompanyBEncryptFunction CompanyCEncryptFunction"
    ["company-query"]="CompanyAQueryFunction CompanyBQueryFunction CompanyCQueryFunction"
    ["company-query-async"]="CompanyAAsyncQueryFunction CompanyBAsyncQueryFunction CompanyCAsyncQueryFunction"
    ["company-query-poll"]="CompanyAPollFunction CompanyBPollFunction CompanyCPollFunction"
    ["company-submit"]="CompanyASubmitFunction CompanyBSubmitFunction CompanyCSubmitFunction"
)

# Base directory
BASE_DIR="/Users/<USER>/Downloads/secure_double_blind_ec"

# Update Makefiles for single-function lambdas
for dir in "${!lambdas[@]}"; do
    function_name="${lambdas[$dir]}"
    makefile="$BASE_DIR/lambdas/$dir/Makefile"
    
    echo "Updating Makefile for $dir -> $function_name"
    
    cat > "$makefile" << EOF
# Include the universal Makefile
include ../../universal.mk

# Function-specific variables
FUNCTION_NAME=$function_name
LAMBDA_PATH=lambdas/$dir

# Target for SAM build
build-$function_name: build
EOF
    
    echo "Updated $makefile"
done

# Update Makefiles for shared lambdas
for dir in "${!shared_lambdas[@]}"; do
    function_names="${shared_lambdas[$dir]}"
    makefile="$BASE_DIR/lambdas/$dir/Makefile"
    first_function=$(echo $function_names | cut -d' ' -f1)
    
    echo "Updating Makefile for $dir -> $function_names"
    
    cat > "$makefile" << EOF
# Include the universal Makefile
include ../../universal.mk

# Function-specific variables
FUNCTION_NAME=$first_function
LAMBDA_PATH=lambdas/$dir

# Targets for SAM build
.PHONY: $function_names
EOF

    for func in $function_names; do
        cat >> "$makefile" << EOF
build-$func: build
EOF
    done
    
    echo "Updated $makefile"
done

echo "All Makefiles updated successfully!"
