#!/bin/bash

# Update all Makefiles to build from root directory

# Get the directory name from the current path
get_dir_name() {
    basename "$(pwd)"
}

# Function name mappings
declare -A FUNCTION_MAP
FUNCTION_MAP["central-store"]="CentralStoreFunction"
FUNCTION_MAP["central-create-institution"]="CentralCreateInstitutionFunction"
FUNCTION_MAP["central-cross-query"]="CentralCrossQueryFunction"
FUNCTION_MAP["central-get-data"]="CentralGetDataFunction"
FUNCTION_MAP["company-decrypt"]="CompanyDecryptFunction"
FUNCTION_MAP["company-submit"]="CompanyASubmitFunction CompanyBSubmitFunction CompanyCSubmitFunction"
FUNCTION_MAP["company-encrypt"]="CompanyAEncryptFunction CompanyBEncryptFunction CompanyCEncryptFunction"
FUNCTION_MAP["company-query"]="CompanyAQueryFunction CompanyBQueryFunction CompanyCQueryFunction"
FUNCTION_MAP["company-query-async"]="CompanyAAsyncQueryFunction CompanyBAsyncQueryFunction CompanyCAsyncQueryFunction"
FUNCTION_MAP["company-query-poll"]="CompanyAPollFunction CompanyBPollFunction CompanyCPollFunction"
FUNCTION_MAP["db-migration"]="DatabaseMigrationFunction"

# Base directory
BASE_DIR="/Users/<USER>/Downloads/secure_double_blind_ec/lambdas"

for dir in central-store central-create-institution central-cross-query central-get-data company-decrypt company-submit company-encrypt company-query company-query-async company-query-poll db-migration; do
    functions=${FUNCTION_MAP[$dir]}
    
    echo "Updating Makefile for $dir with functions: $functions"
    
    # Create the phony targets line
    phony_targets="build"
    build_targets=""
    for func in $functions; do
        phony_targets="$phony_targets build-$func"
        build_targets="$build_targets build-$func"
    done
    
    # Get first function for default build target
    first_func=$(echo $functions | awk '{print $1}')
    
    cat > "$BASE_DIR/$dir/Makefile" << EOF
.PHONY: $phony_targets

# Go build parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

build: build-$first_func

$build_targets:
	@echo "Building lambda function in $dir..."
	cd ../.. && GOOS=\$(GOOS) GOARCH=\$(GOARCH) CGO_ENABLED=\$(CGO_ENABLED) go build -ldflags="-s -w" -o ./lambdas/$dir/bootstrap ./lambdas/$dir/main.go
	@echo "Build completed!"

clean:
	@echo "Cleaning build artifacts..."
	rm -f bootstrap
EOF
    
    echo "Updated $BASE_DIR/$dir/Makefile"
done

echo "All Makefiles updated successfully!"
