const converter = require('openapi-to-postmancollection');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const STACK_NAME = process.env.STACK_NAME || 'secure-double-blind-ec-dev';
const OUTPUT_DIR = './postman-collections';

// Colors for console output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`);

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Function to get CloudFormation stack outputs
async function getStackOutputs(stackName) {
    const { spawn } = require('child_process');
    
    return new Promise((resolve, reject) => {
        const aws = spawn('aws', [
            'cloudformation', 'describe-stacks',
            '--stack-name', stackName,
            '--query', 'Stacks[0].Outputs',
            '--output', 'json'
        ]);
        
        let stdout = '';
        let stderr = '';
        
        aws.stdout.on('data', (data) => stdout += data);
        aws.stderr.on('data', (data) => stderr += data);
        
        aws.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`AWS CLI failed: ${stderr}`));
                return;
            }
            
            try {
                const outputs = JSON.parse(stdout);
                resolve(outputs);
            } catch (error) {
                reject(new Error(`Failed to parse AWS output: ${error.message}`));
            }
        });
    });
}

// Function to get output value by key
function getOutputValue(outputs, key) {
    const output = outputs.find(o => o.OutputKey === key);
    return output ? output.OutputValue : null;
}

// Function to download OpenAPI specification
async function downloadOpenApiSpec(url) {
    try {
        log('yellow', `📥 Downloading OpenAPI spec from: ${url}`);
        const response = await axios.get(url);
        return response.data;
    } catch (error) {
        throw new Error(`Failed to download OpenAPI spec: ${error.message}`);
    }
}

// Function to convert OpenAPI to Postman collection
async function convertToPostman(apiName, openApiSpec, outputPath) {
    return new Promise((resolve, reject) => {
        log('yellow', `🔄 Converting ${apiName} to Postman collection...`);
        
        converter.convert({ type: 'json', data: openApiSpec }, {}, (err, conversionResult) => {
            if (err || !conversionResult.result) {
                reject(new Error(`Conversion failed: ${err || 'Unknown error'}`));
                return;
            }
            
            const collection = conversionResult.output[0].data;
            
            // Enhance collection with authentication and variables
            collection.auth = {
                type: 'apikey',
                apikey: [
                    { key: 'key', value: 'x-api-key', type: 'string' },
                    { key: 'value', value: '{{api_key}}', type: 'string' },
                    { key: 'in', value: 'header', type: 'string' }
                ]
            };
            
            collection.variable = [
                { key: 'base_url', value: '{{base_url}}', type: 'string' },
                { key: 'api_key', value: '{{api_key}}', type: 'string' },
                { key: 'environment', value: 'dev', type: 'string' }
            ];
            
            // Add description with sample data
            collection.info.description = `${collection.info.description}

## Sample Test Data

### Sample Phone Numbers
- +**********
- +**********
- +**********

### Sample Email Addresses
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Sample Account Numbers
- ACC**********
- ACC9876543210
- ACC5555000123

### Sample Fraud Types
- credit_card_fraud
- identity_theft
- account_takeover
- synthetic_identity
- money_laundering

## Environment Variables Required
- \`base_url\`: API Gateway base URL
- \`api_key\`: Your API key for authentication
- \`environment\`: Environment (dev/staging/prod)`;
            
            // Write collection to file
            fs.writeFileSync(outputPath, JSON.stringify(collection, null, 2));
            log('green', `✅ Created: ${outputPath}`);
            resolve(outputPath);
        });
    });
}

// Function to create Postman environment file
function createPostmanEnvironment(outputs, outputPath) {
    log('yellow', '🌍 Creating Postman environment...');
    
    const environment = {
        id: require('crypto').randomUUID(),
        name: `Secure Double Blind EC - ${STACK_NAME}`,
        values: [
            {
                key: 'central_server_url',
                value: getOutputValue(outputs, 'CentralServerApiUrl') || '',
                enabled: true,
                type: 'default'
            },
            {
                key: 'company_a_url',
                value: getOutputValue(outputs, 'CompanyAServerApiUrl') || '',
                enabled: true,
                type: 'default'
            },
            {
                key: 'company_b_url',
                value: getOutputValue(outputs, 'CompanyBServerApiUrl') || '',
                enabled: true,
                type: 'default'
            },
            {
                key: 'company_c_url',
                value: getOutputValue(outputs, 'CompanyCServerApiUrl') || '',
                enabled: true,
                type: 'default'
            },
            {
                key: 'api_key',
                value: 'your-api-key-here',
                enabled: true,
                type: 'secret'
            },
            {
                key: 'environment',
                value: 'dev',
                enabled: true,
                type: 'default'
            },
            {
                key: 'sample_phone',
                value: '+**********',
                enabled: true,
                type: 'default'
            },
            {
                key: 'sample_email',
                value: '<EMAIL>',
                enabled: true,
                type: 'default'
            },
            {
                key: 'sample_account',
                value: 'ACC**********',
                enabled: true,
                type: 'default'
            }
        ],
        _postman_variable_scope: 'environment',
        _postman_exported_at: new Date().toISOString(),
        _postman_exported_using: 'Export Script v1.0.0'
    };
    
    fs.writeFileSync(outputPath, JSON.stringify(environment, null, 2));
    log('green', `✅ Created: ${outputPath}`);
}

// Main execution function
async function main() {
    try {
        log('blue', '🚀 Starting Postman collection export...');
        log('blue', `Stack Name: ${STACK_NAME}`);
        log('blue', `Output Directory: ${OUTPUT_DIR}`);
        
        // Get stack outputs
        log('yellow', '📤 Getting CloudFormation stack outputs...');
        const outputs = await getStackOutputs(STACK_NAME);
        
        // Extract OpenAPI URLs
        const apis = [
            {
                name: 'central-server',
                urlKey: 'CentralServerOpenApiUrl',
                filename: 'central-server-api.postman_collection.json'
            },
            {
                name: 'company-a',
                urlKey: 'CompanyAServerOpenApiUrl',
                filename: 'company-a-api.postman_collection.json'
            },
            {
                name: 'company-b',
                urlKey: 'CompanyBServerOpenApiUrl',
                filename: 'company-b-api.postman_collection.json'
            },
            {
                name: 'company-c',
                urlKey: 'CompanyCServerOpenApiUrl',
                filename: 'company-c-api.postman_collection.json'
            }
        ];
        
        // Process each API
        for (const api of apis) {
            const openApiUrl = getOutputValue(outputs, api.urlKey);
            if (!openApiUrl) {
                log('red', `❌ Could not find ${api.urlKey} in stack outputs`);
                continue;
            }
            
            try {
                const openApiSpec = await downloadOpenApiSpec(openApiUrl);
                const outputPath = path.join(OUTPUT_DIR, api.filename);
                await convertToPostman(api.name, openApiSpec, outputPath);
            } catch (error) {
                log('red', `❌ Failed to process ${api.name}: ${error.message}`);
            }
        }
        
        // Create Postman environment
        const envPath = path.join(OUTPUT_DIR, 'postman-environment.json');
        createPostmanEnvironment(outputs, envPath);
        
        // Create README with instructions
        const readmePath = path.join(OUTPUT_DIR, 'README.md');
        const readmeContent = `# Postman Collections - Secure Double Blind EC System

## Generated Collections

This directory contains Postman collections and environment files exported from the OpenAPI specifications of the Secure Double Blind Elliptic Curve fraud detection system.

## Files

- \`central-server-api.postman_collection.json\` - Central server API collection
- \`company-a-api.postman_collection.json\` - Company A server API collection
- \`company-b-api.postman_collection.json\` - Company B server API collection
- \`company-c-api.postman_collection.json\` - Company C server API collection
- \`postman-environment.json\` - Environment variables for all APIs

## Import Instructions

1. **Open Postman**
2. **Import Collections:**
   - Click the "Import" button
   - Select all \`.postman_collection.json\` files
   - Click "Import"

3. **Import Environment:**
   - Click the "Import" button
   - Select \`postman-environment.json\`
   - Click "Import"

4. **Configure Environment:**
   - Select the imported environment from the dropdown
   - Update the \`api_key\` variable with your actual API key
   - Verify all URLs are correct

## Sample Test Data

The environment includes sample test data:
- \`sample_phone\`: +**********
- \`sample_email\`: <EMAIL>
- \`sample_account\`: ACC**********

## API Documentation

Access the live Swagger documentation:

${apis.map(api => `- **${api.name.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}:** ${getOutputValue(outputs, api.urlKey.replace('OpenApiUrl', 'SwaggerUrl')) || 'URL not available'}`).join('\n')}

## Usage Examples

### 1. Submit Fraud Data (Company A)
\`\`\`json
POST /company-a/submit
{
  "phone": "{{sample_phone}}",
  "email": "{{sample_email}}",
  "account_number": "{{sample_account}}",
  "incident_type": "credit_card_fraud",
  "description": "Suspicious transaction pattern detected"
}
\`\`\`

### 2. Query for Fraud Matches
\`\`\`json
POST /company-a/query
{
  "identifiers": {
    "phone": "{{sample_phone}}",
    "email": "{{sample_email}}"
  },
  "target_companies": ["company_b", "company_c"]
}
\`\`\`

### 3. Encrypt Identifiers
\`\`\`json
POST /company-a/encrypt
{
  "identifiers": {
    "phone": "{{sample_phone}}",
    "email": "{{sample_email}}",
    "account_number": "{{sample_account}}"
  }
}
\`\`\`

## Authentication

All requests require API key authentication. Add your API key to the environment variable \`api_key\`.

## Generated: ${new Date().toISOString()}
`;
        
        fs.writeFileSync(readmePath, readmeContent);
        log('green', `✅ Created: ${readmePath}`);
        
        log('green', '\n🎉 Export completed successfully!');
        log('blue', `📁 Files created in: ${path.resolve(OUTPUT_DIR)}`);
        
        // Print summary
        log('yellow', '\n📋 Summary:');
        log('green', `   ✅ ${apis.length} Postman collections created`);
        log('green', '   ✅ Environment file created');
        log('green', '   ✅ README documentation created');
        
        log('yellow', '\n🔗 Swagger Documentation URLs:');
        apis.forEach(api => {
            const swaggerUrl = getOutputValue(outputs, api.urlKey.replace('OpenApiUrl', 'SwaggerUrl'));
            if (swaggerUrl) {
                log('blue', `   ${api.name.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}: ${swaggerUrl}`);
            }
        });
        
    } catch (error) {
        log('red', `❌ Export failed: ${error.message}`);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { main, getStackOutputs, convertToPostman };
