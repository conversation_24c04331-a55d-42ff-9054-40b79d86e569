-- Migration script to add response_time_ms column to fraud_data table
-- Run this script on existing databases to add the new column

-- Add response_time_ms column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='fraud_data' AND column_name='response_time_ms'
    ) THEN
        ALTER TABLE fraud_data ADD COLUMN response_time_ms INTEGER;
        RAISE NOTICE 'Added response_time_ms column to fraud_data table';
    ELSE
        RAISE NOTICE 'response_time_ms column already exists in fraud_data table';
    END IF;
END
$$; 