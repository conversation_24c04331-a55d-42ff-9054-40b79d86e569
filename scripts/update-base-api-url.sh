#!/bin/bash

# --- CONFIG ---
STACK_NAME="secure-double-blind-ec"
REGION="ap-south-1"
PROFILE="default"  # Change if needed
# --------------

echo "🔍 Fetching current API URL..."
API_URL=$(aws cloudformation describe-stacks \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --profile "$PROFILE" \
  --query "Stacks[0].Outputs[?OutputKey=='ApiUrl'].OutputValue" \
  --output text)

if [[ -z "$API_URL" ]]; then
  echo "❌ Could not retrieve ApiUrl from stack outputs."
  exit 1
fi

echo "✅ API URL retrieved: $API_URL"

echo "🚀 Redeploying stack with updated BASE_API_URL..."

sam deploy \
  --stack-name "$STACK_NAME" \
  --region "$REGION" \
  --profile "$PROFILE" \
  --capabilities CAPABILITY_IAM \
  --parameter-overrides BaseApiUrl="$API_URL"

echo "✅ Stack updated with new BASE_API_URL!"
