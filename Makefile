.PHONY: build clean deploy

# Go parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

# Lambda function directories
LAMBDA_DIRS := $(wildcard lambdas/*)

# Build all Lambda functions
build: clean
	@echo "Building Lambda functions..."
	@for dir in $(LAMBDA_DIRS); do \
		echo "Building $$dir..."; \
		(cd $$dir && \
		GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) go build -ldflags="-s -w" -o bootstrap main.go); \
	done
	@echo "All Lambda functions built successfully!"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@for dir in $(LAMBDA_DIRS); do \
		rm -f $$dir/bootstrap; \
	done
	@echo "Clean completed!"

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod tidy
	go mod download
	@echo "Dependencies downloaded!"

# Validate SAM template
validate:
	@echo "Validating SAM template..."
	sam validate --template template.yaml
	@echo "Template validation completed!"

# Build using SAM
sam-build: build
	@echo "Building with SAM..."
	sam build
	@echo "SAM build completed!"

# Deploy using SAM
deploy: sam-build
	@echo "Deploying with SAM..."
	sam deploy --guided
	@echo "Deployment completed!"

# Deploy with no prompts (for CI/CD)
deploy-ci: sam-build
	@echo "Deploying with SAM (CI mode)..."
	sam deploy --no-confirm-changeset --no-fail-on-empty-changeset
	@echo "Deployment completed!"

# Local development
local-start: sam-build
	@echo "Starting local API..."
	sam local start-api
	@echo "Local API started!"

# Run tests
test:
	@echo "Running tests..."
	go test ./...
	@echo "Tests completed!"

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...
	@echo "Code formatting completed!"

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run
	@echo "Linting completed!"

# Create deployment package
package: build
	@echo "Creating deployment packages..."
	@for dir in $(LAMBDA_DIRS); do \
		echo "Packaging $$dir..."; \
		(cd $$dir && \
		zip -r ../`basename $$dir`.zip bootstrap); \
	done
	@echo "Packaging completed!"

# Initialize company configurations
init-companies:
	@echo "Creating company configuration files..."
	@mkdir -p configs
	@echo "COMPANY_A_KEY=0123456789abcdef0123456789abcdef" > configs/.env.company_a
	@echo "COMPANY_A_SALT=fedcba9876543210fedcba9876543210" >> configs/.env.company_a
	@echo "COMPANY_B_KEY=abcdef0123456789abcdef0123456789" > configs/.env.company_b
	@echo "COMPANY_B_SALT=9876543210fedcba9876543210fedcba" >> configs/.env.company_b
	@echo "COMPANY_C_KEY=56789abcdef012356789abcdef012345" > configs/.env.company_c
	@echo "COMPANY_C_SALT=210fedcba9876543210fedcba9876543" >> configs/.env.company_c
	@echo "Company configurations created in configs/ directory"

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build all Lambda functions"
	@echo "  clean          - Clean build artifacts"
	@echo "  deps           - Download Go dependencies"
	@echo "  validate       - Validate SAM template"
	@echo "  sam-build      - Build using SAM"
	@echo "  deploy         - Deploy using SAM (guided)"
	@echo "  deploy-ci      - Deploy using SAM (no prompts)"
	@echo "  local-start    - Start local API for development"
	@echo "  test           - Run tests"
	@echo "  fmt            - Format Go code"
	@echo "  lint           - Lint Go code"
	@echo "  package        - Create deployment packages"
	@echo "  init-companies - Initialize company configurations"
	@echo "  help           - Show this help message"
