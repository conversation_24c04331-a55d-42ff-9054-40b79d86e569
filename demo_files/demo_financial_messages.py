#!/usr/bin/env python3
"""
Demo script showing hybrid processing on actual financial messages.
"""

import asyncio
import csv
import json
from hybrid_sms_processor import HybridSMSProcessor

async def demo_financial_messages():
    """Demo with actual financial messages to show OpenAI integration."""
    
    # Real financial messages from the SMS backup
    financial_messages = [
        {
            'original_id': '171448832312176',
            'phone_number': 'xx87463829',
            'sender_address': 'JD-SBIUPI',
            'timestamp': 'Apr 30, 2024 8:15:23 PM',
            'original_text': 'Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI'
        },
        {
            'original_id': '***************',
            'phone_number': 'xx87463829',
            'sender_address': 'JM-RBLBNK',
            'timestamp': 'Apr 30, 2024 9:07:36 PM',
            'original_text': 'Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank'
        },
        {
            'original_id': '****************',
            'phone_number': 'xx87463829',
            'sender_address': 'AX-SBIUPI',
            'timestamp': 'Apr 30, 2024 9:44:24 PM',
            'original_text': 'Dear UPI user A/C X4884 debited by 135.46 on date 30Apr24 trf to MCDONALDS Refno ************. If not u? call **********. -SBI'
        }
    ]
    
    # Non-financial messages for comparison
    non_financial_messages = [
        {
            'original_id': '****************',
            'phone_number': 'xx87463829',
            'sender_address': 'JX-JioSvc',
            'timestamp': 'Apr 30, 2024 5:59:33 PM',
            'original_text': 'For seamless data experience across the country, set the \'Data Roaming\' as On/Always. To know \'How to Set up Jio Network\' on mobile, click https://youtu.be/o18LboDi1ho To know your number, track balance & usage, give a miss call to 1299.'
        },
        {
            'original_id': '****************',
            'phone_number': 'xx87463829',
            'sender_address': 'VM-SBICRD',
            'timestamp': 'Apr 30, 2024 9:20:23 PM',
            'original_text': 'Get an Amazon Voucher worth Rs.500 with every Add-on Card. Apply before 30 June 2024 to avail offer. Apply: https://sbicard.com/addon T&C - SBI Card'
        }
    ]
    
    all_messages = financial_messages + non_financial_messages
    
    print("FINANCIAL MESSAGES DEMO - HYBRID PROCESSING")
    print("=" * 70)
    print("This demo shows the hybrid approach on real financial transactions")
    print("=" * 70)
    print()
    
    # Initialize processor with OpenAI
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    processor = HybridSMSProcessor(OPENAI_API_KEY)
    
    results = []
    financial_count = 0
    non_financial_count = 0
    openai_calls = 0
    
    for i, message in enumerate(all_messages, 1):
        print(f"Message {i}: {message['sender_address']}")
        print(f"Text: {message['original_text'][:80]}{'...' if len(message['original_text']) > 80 else ''}")
        print()
        
        try:
            result = await processor.process_sms(message)
            results.append(result)
            
            classification = result.get('classification', 'unknown')
            print(f"  📊 Classification: {classification.upper()}")
            print(f"  🏷️  Rule-based Type: {result.get('sms_type', 'unknown')}")
            print(f"  🤖 OpenAI Type: {result.get('classified_type', 'unknown')}")
            print(f"  💰 Amount: {result.get('amount', 'N/A')}")
            print(f"  🏢 Entity: {result.get('entity_name', 'N/A')}")
            print(f"  🔢 Account: {result.get('account_number', 'N/A')}")
            print(f"  📄 Txn Ref: {result.get('txn_ref', 'N/A')}")
            
            if classification == 'financial':
                financial_count += 1
                # Check if OpenAI was used
                extracted_data = result.get('extracted_data_json', '{}')
                if extracted_data != '{}':
                    try:
                        data = json.loads(extracted_data)
                        if len(data) > 5:  # Rich extraction indicates OpenAI
                            openai_calls += 1
                            print(f"  🧠 OpenAI extracted: {', '.join([k for k, v in data.items() if v is not None])}")
                    except:
                        pass
            elif classification == 'non-financial':
                non_financial_count += 1
                print(f"  ⚡ Fast rule-based processing (no OpenAI call needed)")
            
        except Exception as e:
            print(f"  ❌ ERROR: {e}")
            results.append({'original_id': message['original_id'], 'error': str(e)})
        
        print("-" * 70)
        print()
    
    # Summary
    print("🎯 DEMO RESULTS SUMMARY")
    print("=" * 70)
    print(f"📨 Total messages processed: {len(results)}")
    print(f"💳 Financial messages: {financial_count}")
    print(f"📢 Non-financial messages: {non_financial_count}")
    print(f"🤖 OpenAI API calls made: {openai_calls}")
    print(f"💡 Cost efficiency: {openai_calls}/{len(results)} = {openai_calls/len(results)*100:.1f}% of messages used OpenAI")
    print()
    
    print("✨ KEY BENEFITS DEMONSTRATED:")
    print("✅ Rule-based classification correctly identifies financial vs non-financial")
    print("✅ OpenAI extraction only called for financial messages (cost-effective)")
    print("✅ Rich field extraction for financial transactions (amount, entity, account, etc.)")
    print("✅ Non-financial messages processed quickly without OpenAI calls")
    print("✅ Combined approach gives best accuracy and cost efficiency")
    print()
    
    # Save demo results
    if results:
        all_fields = set()
        for result in results:
            all_fields.update(result.keys())
        
        fieldnames = sorted(list(all_fields))
        
        with open('demo_financial_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print(f"📄 Demo results saved to 'demo_financial_results.csv'")
    
    return results

if __name__ == "__main__":
    asyncio.run(demo_financial_messages())
