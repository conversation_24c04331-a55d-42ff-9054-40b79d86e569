// Package crypto provides cryptographic blinding utilities used across the system.
package crypto

import (
	"crypto/sha256"
	"errors"
	"fmt"
	"math/big"

	"github.com/cloudflare/circl/group"
)

// GenerateDeterministicScalar generates a deterministic scalar from key, data, and domain
func GenerateDeterministicScalar(g group.Group, key []byte, data, domain string) (group.Scalar, error) {
	if len(key) < 32 {
		return nil, errors.New("key must be at least 32 bytes")
	}
	
	// Use HKDF-like construction for deterministic but secure scalar generation
	h := sha256.New()
	h.Write(key)
	h.Write([]byte(data))
	h.Write([]byte(domain)) // Domain separator (BLIND, SECONDARY_BLIND, etc.)
	h.Write([]byte("DETERMINISTIC_SCALAR_V1"))
	hash := h.Sum(nil)
	
	// Create scalar from hash - convert bytes to big.Int first
	scalar := g.NewScalar()
	hashInt := new(big.Int).SetBytes(hash)
	scalar.SetBigInt(hashInt)
	
	return scalar, nil
}

// SecureHashToPoint implements a secure hash-to-curve function
func SecureHashToPoint(g group.Group, data string, salt []byte) (group.Element, error) {
	// Use simplified but secure approach: hash to scalar then multiply with generator
	scalar, err := HashToScalar(g, data, salt)
	if err != nil {
		return nil, fmt.Errorf("failed to hash to scalar: %v", err)
	}

	// Use scalar multiplication with generator point
	point := g.NewElement()
	point.MulGen(scalar)

	return point, nil
}

// HashToScalar securely hashes input to a scalar using HMAC-like construction
func HashToScalar(g group.Group, data string, salt []byte) (group.Scalar, error) {
	if len(salt) < 32 {
		return nil, errors.New("salt must be at least 32 bytes")
	}

	// Use HKDF-like construction for better security
	h := sha256.New()
	h.Write(salt)
	h.Write([]byte(data))
	h.Write([]byte("BLIND_SCALAR_V1")) // Domain separator
	hash := h.Sum(nil)

	// Create scalar from hash - convert bytes to big.Int first
	scalar := g.NewScalar()
	hashInt := new(big.Int).SetBytes(hash)
	scalar.SetBigInt(hashInt)

	return scalar, nil
}

// UniversalHashToPoint creates the same base point for all companies given the same data
// This is CRITICAL for cross-company fraud detection to work - all companies must
// map the same identifier to the same elliptic curve point for matching to work
func UniversalHashToPoint(g group.Group, data string) (group.Element, error) {
	// Use a universal salt that all companies share for creating base points
	// This ensures H(data) is the same across all companies
	universalSalt := []byte("UNIVERSAL_FRAUD_DETECTION_SALT_V1_DO_NOT_CHANGE")
	
	// Pad to 64 bytes for extra security
	if len(universalSalt) < 64 {
		padding := make([]byte, 64-len(universalSalt))
		universalSalt = append(universalSalt, padding...)
	}
	
	return SecureHashToPoint(g, data, universalSalt)
}
