package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

type DB struct {
	conn *sql.DB
}

// Institution represents the institution table
type Institution struct {
	ID              int       `json:"id"`
	InstitutionName string    `json:"institution_name"`
	AccessRole      string    `json:"access_role"`
	CreatedBy       string    `json:"created_by"`
	UpdatedBy       *string   `json:"updated_by"`
	Status          string    `json:"status"`
	InstitutionType *string   `json:"institution_type"`
	EncryptionKey   *string   `json:"encryption_key,omitempty"`
	EncryptionSalt  *string   `json:"encryption_salt,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// FraudData represents the fraud_data table
type FraudData struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	UpdatedBy      *string                `json:"updated_by"`
	AssociationID  *int                   `json:"association_id"`
	RowStatus      string                 `json:"row_status,omitempty"`
	ResponseTimeMs *int                   `json:"response_time_ms,omitempty"`
}

// IDAssociation represents the id_associations table
type IDAssociation struct {
	ID           int         `json:"id"`
	Associations interface{} `json:"associations"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
}

// AuditLog represents the audit_logs table
type AuditLog struct {
	ID            int                    `json:"id"`
	EventType     string                 `json:"event_type"`
	InstitutionID *int                   `json:"institution_id"`
	UserID        string                 `json:"user_id"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// BlindedIdentifier represents a single blinded identifier with its type
type BlindedIdentifier struct {
	Identifier string `json:"identifier"`
	Type       string `json:"type"`
}

// TaskLog represents the task_log table
type TaskLog struct {
	ID                int                      `json:"id"`
	TaskID            uuid.UUID                `json:"task_id"`
	TaskType          string                   `json:"task_type"`
	BlindedIdentifiers []BlindedIdentifier     `json:"blinded_identifiers"`
	InstitutionID     *int                     `json:"institution_id"`
	Status            string                   `json:"status"`
	Response          map[string]interface{}   `json:"response"`
	Steps             []map[string]interface{} `json:"steps"`
	Notes             *string                  `json:"notes"`
	Error             *string                  `json:"error"`
	CreatedAt         time.Time                `json:"created_at"`
	UpdatedAt         time.Time                `json:"updated_at"`
	CreatedBy         string                   `json:"created_by"`
	QueryCompletedAt  *time.Time               `json:"query_completed_at"`
	StartTime         *time.Time               `json:"start_time"`
	EndTime           *time.Time               `json:"end_time"`
}

// QueryResponseDetails represents the query_response_details table
type QueryResponseDetails struct {
	ID                    int                    `json:"id"`
	TaskID                uuid.UUID              `json:"task_id"`
	QueryID               string                 `json:"query_id"`
	InstitutionID         int                    `json:"institution_id"`
	OriginalIdentifiers   map[string]interface{} `json:"original_identifiers"`
	BlindedIdentifiers    map[string]interface{} `json:"blinded_identifiers"`
	DoubleEncryptedValues map[string]interface{} `json:"double_encrypted_values"`
	SingleDecryptedValues map[string]interface{} `json:"single_decrypted_values"`
	FraudDataResults      map[string]interface{} `json:"fraud_data_results"`
	ResponseSummary       map[string]interface{} `json:"response_summary"`
	ResponseTimeline      map[string]interface{} `json:"response_timeline"`
	ResponseProcesses     map[string]interface{} `json:"response_processes"`
	ResponseStats         map[string]interface{} `json:"response_stats"`
	ResponseTimeMs        int                    `json:"response_time_ms"`
	Status                string                 `json:"status"`
	CreatedAt             time.Time              `json:"created_at"`
	UpdatedAt             time.Time              `json:"updated_at"`
}

// New creates a new database connection
func New() (*DB, error) {
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	dbname := os.Getenv("DB_NAME")
	schema := os.Getenv("DB_SCHEMA")

	if host == "" {
		host = "localhost"
	}
	if port == "" {
		port = "5432"
	}
	if schema == "" {
		schema = "consortium"
	}

	// For external databases, use sslmode=require instead of prefer
	psqlInfo := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=require search_path=%s",
		host, port, user, password, dbname, schema)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	if err = db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	// Ensure the schema exists and set search_path
	_, err = db.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schema))
	if err != nil {
		return nil, fmt.Errorf("failed to create schema: %v", err)
	}

	_, err = db.Exec(fmt.Sprintf("SET search_path TO %s", schema))
	if err != nil {
		return nil, fmt.Errorf("failed to set search_path: %v", err)
	}

	return &DB{conn: db}, nil
}

// Close closes the database connection
func (db *DB) Close() error {
	return db.conn.Close()
}

// CreateInstitution creates a new institution
func (db *DB) CreateInstitution(inst *Institution) error {
	query := `
		INSERT INTO institution (institution_name, access_role, created_by, status, institution_type, encryption_key, encryption_salt)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at, updated_at`

	err := db.conn.QueryRow(query, inst.InstitutionName, inst.AccessRole, inst.CreatedBy, inst.Status, inst.InstitutionType, inst.EncryptionKey, inst.EncryptionSalt).
		Scan(&inst.ID, &inst.CreatedAt, &inst.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create institution: %v", err)
	}

	return nil
}

// GetInstitutionByName gets an institution by name
func (db *DB) GetInstitutionByName(name string) (*Institution, error) {
	inst := &Institution{}
	query := `
		SELECT id, institution_name, access_role, created_by, updated_by, status, institution_type, encryption_key, encryption_salt, created_at, updated_at
		FROM institution
		WHERE institution_name = $1`

	err := db.conn.QueryRow(query, name).Scan(
		&inst.ID, &inst.InstitutionName, &inst.AccessRole, &inst.CreatedBy,
		&inst.UpdatedBy, &inst.Status, &inst.InstitutionType, &inst.EncryptionKey, &inst.EncryptionSalt, &inst.CreatedAt, &inst.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to get institution: %v", err)
	}

	return inst, nil
}

// GetInstitutionByID gets an institution by ID
func (db *DB) GetInstitutionByID(id int) (*Institution, error) {
	inst := &Institution{}
	query := `
		SELECT id, institution_name, access_role, created_by, updated_by, status, institution_type, encryption_key, encryption_salt, created_at, updated_at
		FROM institution
		WHERE id = $1`

	err := db.conn.QueryRow(query, id).Scan(
		&inst.ID, &inst.InstitutionName, &inst.AccessRole, &inst.CreatedBy,
		&inst.UpdatedBy, &inst.Status, &inst.InstitutionType, &inst.EncryptionKey, &inst.EncryptionSalt, &inst.CreatedAt, &inst.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to get institution: %v", err)
	}

	return inst, nil
}

// UpdateInstitutionEncryptionKeys updates an institution's encryption keys
func (db *DB) UpdateInstitutionEncryptionKeys(id int, key, salt string) error {
	query := `
		UPDATE institution
		SET encryption_key = $2, encryption_salt = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`

	_, err := db.conn.Exec(query, id, key, salt)
	if err != nil {
		return fmt.Errorf("failed to update institution encryption keys: %v", err)
	}

	return nil
}

// ListInstitutions returns all institutions
func (db *DB) ListInstitutions() ([]*Institution, error) {
	query := `
		SELECT id, institution_name, access_role, created_by, updated_by, status, institution_type, encryption_key, encryption_salt, created_at, updated_at
		FROM institution`

	rows, err := db.conn.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query institutions: %v", err)
	}
	defer rows.Close()

	var institutions []*Institution
	for rows.Next() {
		inst := &Institution{}
		err := rows.Scan(
			&inst.ID, &inst.InstitutionName, &inst.AccessRole, &inst.CreatedBy,
			&inst.UpdatedBy, &inst.Status, &inst.InstitutionType, &inst.EncryptionKey, &inst.EncryptionSalt, &inst.CreatedAt, &inst.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan institution: %v", err)
		}
		institutions = append(institutions, inst)
	}
	return institutions, nil
}

// CreateFraudData creates a new fraud data record
func (db *DB) CreateFraudData(fraud *FraudData) error {
	metadataJSON, err := json.Marshal(fraud.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}

	query := `
		INSERT INTO fraud_data (identifier, identifier_type, metadata, institution_id, created_by, status, fraud_type, association_id, row_status, response_time_ms)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING id, created_at, updated_at`

	// Set default row_status if not provided
	if fraud.RowStatus == "" {
		fraud.RowStatus = "active"
	}

	err = db.conn.QueryRow(query,
		fraud.Identifier, fraud.IdentifierType, metadataJSON, fraud.InstitutionID,
		fraud.CreatedBy, fraud.Status, fraud.FraudType, fraud.AssociationID, fraud.RowStatus, fraud.ResponseTimeMs).
		Scan(&fraud.ID, &fraud.CreatedAt, &fraud.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create fraud data: %v", err)
	}

	return nil
}

// GetFraudDataByIdentifier gets fraud data by identifier
func (db *DB) GetFraudDataByIdentifier(identifier string) ([]*FraudData, error) {
	query := `
		SELECT id, identifier, identifier_type, metadata, institution_id, created_by, 
		       created_at, updated_at, status, fraud_type, updated_by, association_id, row_status, response_time_ms
		FROM fraud_data
		WHERE identifier = $1`

	rows, err := db.conn.Query(query, identifier)
	if err != nil {
		return nil, fmt.Errorf("failed to query fraud data: %v", err)
	}
	defer rows.Close()

	var results []*FraudData
	for rows.Next() {
		fraud := &FraudData{}
		var metadataJSON []byte

		err := rows.Scan(
			&fraud.ID, &fraud.Identifier, &fraud.IdentifierType, &metadataJSON,
			&fraud.InstitutionID, &fraud.CreatedBy, &fraud.CreatedAt, &fraud.UpdatedAt,
			&fraud.Status, &fraud.FraudType, &fraud.UpdatedBy, &fraud.AssociationID, &fraud.RowStatus, &fraud.ResponseTimeMs)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fraud data: %v", err)
		}

		if err := json.Unmarshal(metadataJSON, &fraud.Metadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %v", err)
		}

		results = append(results, fraud)
	}

	return results, nil
}

// CreateIDAssociation creates a new ID association
func (db *DB) CreateIDAssociation(assoc *IDAssociation) error {
	associationsJSON, err := json.Marshal(assoc.Associations)
	if err != nil {
		return fmt.Errorf("failed to marshal associations: %v", err)
	}

	query := `
		INSERT INTO id_associations (associations)
		VALUES ($1)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query, associationsJSON).
		Scan(&assoc.ID, &assoc.CreatedAt, &assoc.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create ID association: %v", err)
	}

	return nil
}

// CreateAuditLog creates a new audit log entry
func (db *DB) CreateAuditLog(log *AuditLog) error {
	metadataJSON, err := json.Marshal(log.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}

	query := `
		INSERT INTO audit_logs (event_type, institution_id, user_id, metadata)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query, log.EventType, log.InstitutionID, log.UserID, metadataJSON).
		Scan(&log.ID, &log.CreatedAt, &log.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create audit log: %v", err)
	}

	return nil
}

// GetFraudDataByAssociationID gets fraud data by association ID
func (db *DB) GetFraudDataByAssociationID(associationID int) ([]*FraudData, error) {
	query := `
		SELECT id, identifier, identifier_type, metadata, institution_id, created_by, 
		       created_at, updated_at, status, fraud_type, updated_by, association_id, row_status, response_time_ms
		FROM fraud_data
		WHERE association_id = $1`

	rows, err := db.conn.Query(query, associationID)
	if err != nil {
		return nil, fmt.Errorf("failed to query fraud data by association ID: %v", err)
	}
	defer rows.Close()

	var results []*FraudData
	for rows.Next() {
		fraud := &FraudData{}
		var metadataJSON []byte

		err := rows.Scan(
			&fraud.ID, &fraud.Identifier, &fraud.IdentifierType, &metadataJSON,
			&fraud.InstitutionID, &fraud.CreatedBy, &fraud.CreatedAt, &fraud.UpdatedAt,
			&fraud.Status, &fraud.FraudType, &fraud.UpdatedBy, &fraud.AssociationID, &fraud.RowStatus, &fraud.ResponseTimeMs)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fraud data: %v", err)
		}

		if err := json.Unmarshal(metadataJSON, &fraud.Metadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %v", err)
		}

		results = append(results, fraud)
	}

	return results, nil
}

// CreateTaskLog creates a new task log entry
func (db *DB) CreateTaskLog(taskLog *TaskLog) error {
	responseJSON, err := json.Marshal(taskLog.Response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %v", err)
	}

	stepsJSON, err := json.Marshal(taskLog.Steps)
	if err != nil {
		return fmt.Errorf("failed to marshal steps: %v", err)
	}

	blindedIdentifiersJSON, err := json.Marshal(taskLog.BlindedIdentifiers)
	if err != nil {
		return fmt.Errorf("failed to marshal blinded identifiers: %v", err)
	}

	query := `
		INSERT INTO task_log (task_id, task_type, blinded_identifiers, institution_id, status, response, steps, notes, error, created_by, start_time)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query,
		taskLog.TaskID, taskLog.TaskType, blindedIdentifiersJSON, taskLog.InstitutionID, taskLog.Status,
		responseJSON, stepsJSON, taskLog.Notes, taskLog.Error, taskLog.CreatedBy, taskLog.StartTime).
		Scan(&taskLog.ID, &taskLog.CreatedAt, &taskLog.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create task log: %v", err)
	}

	return nil
}

// GetTaskLogByTaskID gets a task log by task ID
func (db *DB) GetTaskLogByTaskID(taskID uuid.UUID) (*TaskLog, error) {
	taskLog := &TaskLog{}
	var responseJSON, stepsJSON, blindedIdentifiersJSON []byte
	var institutionID sql.NullInt64

	query := `
		SELECT id, task_id, task_type, blinded_identifiers, institution_id, status, response, steps, notes, error, 
		       created_at, updated_at, created_by, query_completed_at, start_time, end_time
		FROM task_log
		WHERE task_id = $1`

	err := db.conn.QueryRow(query, taskID).Scan(
		&taskLog.ID, &taskLog.TaskID, &taskLog.TaskType, &blindedIdentifiersJSON,
		&institutionID, &taskLog.Status, &responseJSON, &stepsJSON, &taskLog.Notes, &taskLog.Error,
		&taskLog.CreatedAt, &taskLog.UpdatedAt, &taskLog.CreatedBy,
		&taskLog.QueryCompletedAt, &taskLog.StartTime, &taskLog.EndTime)

	if err != nil {
		return nil, fmt.Errorf("failed to get task log: %v", err)
	}

	if institutionID.Valid {
		instID := int(institutionID.Int64)
		taskLog.InstitutionID = &instID
	}

	if err := json.Unmarshal(responseJSON, &taskLog.Response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if err := json.Unmarshal(stepsJSON, &taskLog.Steps); err != nil {
		return nil, fmt.Errorf("failed to unmarshal steps: %v", err)
	}

	if err := json.Unmarshal(blindedIdentifiersJSON, &taskLog.BlindedIdentifiers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal blinded identifiers: %v", err)
	}

	return taskLog, nil
}

// UpdateTaskLog updates an existing task log
func (db *DB) UpdateTaskLog(taskLog *TaskLog) error {
	responseJSON, err := json.Marshal(taskLog.Response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %v", err)
	}

	stepsJSON, err := json.Marshal(taskLog.Steps)
	if err != nil {
		return fmt.Errorf("failed to marshal steps: %v", err)
	}

	blindedIdentifiersJSON, err := json.Marshal(taskLog.BlindedIdentifiers)
	if err != nil {
		return fmt.Errorf("failed to marshal blinded identifiers: %v", err)
	}

	query := `
		UPDATE task_log 
		SET status = $1, response = $2, steps = $3, notes = $4, error = $5, 
		    query_completed_at = $6, end_time = $7, blinded_identifiers = $8
		WHERE task_id = $9`

	_, err = db.conn.Exec(query,
		taskLog.Status, responseJSON, stepsJSON, taskLog.Notes, taskLog.Error,
		taskLog.QueryCompletedAt, taskLog.EndTime, blindedIdentifiersJSON, taskLog.TaskID)

	if err != nil {
		return fmt.Errorf("failed to update task log: %v", err)
	}

	return nil
}

// AddStepToTaskLog adds a new step to an existing task log
func (db *DB) AddStepToTaskLog(taskID uuid.UUID, step map[string]interface{}) error {
	query := `
		UPDATE task_log 
		SET steps = steps || $1::jsonb
		WHERE task_id = $2`

	stepJSON, err := json.Marshal([]map[string]interface{}{step})
	if err != nil {
		return fmt.Errorf("failed to marshal step: %v", err)
	}

	_, err = db.conn.Exec(query, stepJSON, taskID)
	if err != nil {
		return fmt.Errorf("failed to add step to task log: %v", err)
	}

	return nil
}

// GetInstitutionsWithFraudData returns distinct institution IDs that have fraud data
func (db *DB) GetInstitutionsWithFraudData() ([]int, error) {
	query := `
		SELECT DISTINCT institution_id
		FROM fraud_data;`

	rows, err := db.conn.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query institutions with fraud data: %v", err)
	}
	defer rows.Close()

	var institutionIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, fmt.Errorf("failed to scan institution ID: %v", err)
		}
		institutionIDs = append(institutionIDs, id)
	}

	return institutionIDs, nil
}

// UpdateFraudDataStatus updates the status and fraud_type of a fraud data record
func (db *DB) UpdateFraudDataStatus(id int, status, fraudType, updatedBy string) error {
	query := `
		UPDATE fraud_data 
		SET status = $1, fraud_type = $2, updated_by = $3, updated_at = NOW()
		WHERE id = $4 AND row_status = 'active'`

	result, err := db.conn.Exec(query, status, fraudType, updatedBy, id)
	if err != nil {
		return fmt.Errorf("failed to update fraud data status: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no fraud data found with id %d or record is inactive", id)
	}

	return nil
}

// RevokeFraudData marks a fraud data record as inactive
func (db *DB) RevokeFraudData(id int, revokedBy string) error {
	query := `
		UPDATE fraud_data 
		SET row_status = 'inactive', updated_by = $1, updated_at = NOW()
		WHERE id = $2 AND row_status = 'active'`

	result, err := db.conn.Exec(query, revokedBy, id)
	if err != nil {
		return fmt.Errorf("failed to revoke fraud data: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no active fraud data found with id %d", id)
	}

	return nil
}

// RevokeFraudDataByAssociationID marks all fraud data records with the same association ID as inactive
func (db *DB) RevokeFraudDataByAssociationID(associationID int, revokedBy string) error {
	query := `
		UPDATE fraud_data 
		SET row_status = 'inactive', updated_by = $1, updated_at = NOW()
		WHERE association_id = $2 AND row_status = 'active'`

	result, err := db.conn.Exec(query, revokedBy, associationID)
	if err != nil {
		return fmt.Errorf("failed to revoke fraud data by association ID: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no active fraud data found with association ID %d", associationID)
	}

	return nil
}

// UpdateFraudDataStatusByAssociationID updates the status and fraud_type of all fraud data records with the same association ID
func (db *DB) UpdateFraudDataStatusByAssociationID(associationID int, status, fraudType, updatedBy string) error {
	query := `
		UPDATE fraud_data 
		SET status = $1, fraud_type = $2, updated_by = $3, updated_at = NOW()
		WHERE association_id = $4 AND row_status = 'active'`

	result, err := db.conn.Exec(query, status, fraudType, updatedBy, associationID)
	if err != nil {
		return fmt.Errorf("failed to update fraud data status by association ID: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no active fraud data found with association ID %d", associationID)
	}

	return nil
}

// GetFraudDataByID retrieves a single fraud data record by ID
func (db *DB) GetFraudDataByID(id int) (*FraudData, error) {
	query := `
		SELECT id, identifier, identifier_type, metadata, institution_id, created_by, 
		       created_at, updated_at, status, fraud_type, updated_by, association_id, row_status, response_time_ms
		FROM fraud_data
		WHERE id = $1`

	var fraudData FraudData
	var metadataJSON []byte
	var updatedBy sql.NullString
	var associationID sql.NullInt64
	var rowStatus sql.NullString
	var responseTimeMs sql.NullInt64

	err := db.conn.QueryRow(query, id).Scan(
		&fraudData.ID, &fraudData.Identifier, &fraudData.IdentifierType,
		&metadataJSON, &fraudData.InstitutionID, &fraudData.CreatedBy,
		&fraudData.CreatedAt, &fraudData.UpdatedAt, &fraudData.Status,
		&fraudData.FraudType, &updatedBy, &associationID, &rowStatus, &responseTimeMs)

	if err != nil {
		return nil, fmt.Errorf("failed to get fraud data: %v", err)
	}

	if err := json.Unmarshal(metadataJSON, &fraudData.Metadata); err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata: %v", err)
	}

	if updatedBy.Valid {
		fraudData.UpdatedBy = &updatedBy.String
	}

	if associationID.Valid {
		assocID := int(associationID.Int64)
		fraudData.AssociationID = &assocID
	}

	if rowStatus.Valid {
		fraudData.RowStatus = rowStatus.String
	} else {
		fraudData.RowStatus = "active" // Default value
	}

	if responseTimeMs.Valid {
		responseTime := int(responseTimeMs.Int64)
		fraudData.ResponseTimeMs = &responseTime
	}

	return &fraudData, nil
}

// ListInsertsRequest represents the request parameters for listing inserts
type ListInsertsRequest struct {
	InstitutionID  *int     `json:"institution_id"`
	Status         []string `json:"status"`
	IdentifierType []string `json:"identifier_type"`
	FraudType      []string `json:"fraud_type"`
	FraudStatus    []string `json:"fraud_status"`
	SortBy         string   `json:"sort_by"`
	SortOrder      string   `json:"sort_order"`
	Limit          int      `json:"limit"`
	Offset         int      `json:"offset"`
}

// InsertRecord represents a single insert record for the API response
type InsertRecord struct {
	ID                  int              `json:"id"`
	InsertID            string           `json:"insertId"`
	CreatedDatetime     string           `json:"createdDatetime"`
	LastUpdatedDatetime string           `json:"lastUpdatedDatetime"`
	InstitutionName     string           `json:"institutionName"`
	InstitutionID       int              `json:"institutionId"`
	InstitutionType     string           `json:"institutionType"`
	Status              string           `json:"status"`
	Identifiers         []IdentifierInfo `json:"identifiers"`
	FraudType           string           `json:"fraudType"`
	FraudStatus         string           `json:"fraudStatus"`
	RowStatus           string           `json:"rowStatus"`
	ResponseTimeMs      *int             `json:"responseTimeMs,omitempty"`
}

// IdentifierInfo represents identifier information in the response
type IdentifierInfo struct {
	Encoded string `json:"encoded"`
	Type    string `json:"type"`
}

func (db *DB) ListInsertsWithFilters(req ListInsertsRequest) ([]InsertRecord, int, error) {
	// Build the base query to get fraud data with association info
	baseQuery := `
		SELECT 
			fd.id,
			fd.created_at,
			fd.updated_at,
			fd.identifier,
			fd.identifier_type,
			fd.status,
			fd.fraud_type,
			fd.row_status,
			fd.association_id,
			fd.response_time_ms,
			i.institution_name,
			i.institution_type,
			i.id as institution_id
		FROM fraud_data fd
		LEFT JOIN institution i ON fd.institution_id = i.id
		WHERE fd.row_status = 'active'`

	// Build WHERE clause with filters
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.InstitutionID != nil {
		conditions = append(conditions, fmt.Sprintf("fd.institution_id = $%d", argIndex))
		args = append(args, *req.InstitutionID)
		argIndex++
	}

	if len(req.Status) > 0 {
		placeholders := make([]string, len(req.Status))
		for i := range req.Status {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.Status[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("fd.status IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(req.IdentifierType) > 0 {
		placeholders := make([]string, len(req.IdentifierType))
		for i := range req.IdentifierType {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.IdentifierType[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("fd.identifier_type IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(req.FraudType) > 0 {
		placeholders := make([]string, len(req.FraudType))
		for i := range req.FraudType {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.FraudType[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("fd.fraud_type IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(req.FraudStatus) > 0 {
		placeholders := make([]string, len(req.FraudStatus))
		for i := range req.FraudStatus {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.FraudStatus[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("fd.status IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Get total count of unique inserts (accounting for associations)
	countQuery := `
		SELECT COUNT(DISTINCT 
			CASE 
				WHEN fd.association_id IS NOT NULL THEN fd.association_id
				ELSE fd.id
			END
		) FROM fraud_data fd
		LEFT JOIN institution i ON fd.institution_id = i.id
		WHERE fd.row_status = 'active'`
	
	// Add the same WHERE conditions to count query
	if len(conditions) > 0 {
		countQuery += " AND " + strings.Join(conditions, " AND ")
	}
	
	var total int
	err := db.conn.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Add sorting
	validSortFields := map[string]string{
		"created_at":  "fd.created_at",
		"updated_at":  "fd.updated_at",
		"status":      "fd.status",
		"fraud_type":  "fd.fraud_type",
		"identifier":  "fd.identifier",
		"institution": "i.institution_name",
	}

	sortField := validSortFields["created_at"] // default
	if field, exists := validSortFields[req.SortBy]; exists {
		sortField = field
	}

	sortOrder := "DESC"
	if strings.ToUpper(req.SortOrder) == "ASC" {
		sortOrder = "ASC"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortField, sortOrder)

	// Add pagination
	if req.Limit > 0 {
		baseQuery += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, req.Limit)
		argIndex++
	}

	if req.Offset > 0 {
		baseQuery += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, req.Offset)
	}

	// Execute the query
	rows, err := db.conn.Query(baseQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query inserts: %v", err)
	}
	defer rows.Close()

	// Map to group records by association
	associationGroups := make(map[int][]InsertRecord)
	nonAssociatedRecords := []InsertRecord{}

	for rows.Next() {
		var record InsertRecord
		var id int
		var institutionID int
		var createdAt, updatedAt time.Time
		var identifier, identifierType, status, fraudType, rowStatus string
		var institutionName, institutionType sql.NullString
		var associationID sql.NullInt64
		var responseTimeMs sql.NullInt64

		err := rows.Scan(
			&id, &createdAt, &updatedAt, &identifier, &identifierType,
			&status, &fraudType, &rowStatus, &associationID, &responseTimeMs, &institutionName, &institutionType, &institutionID)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan insert record: %v", err)
		}

		// Format the record
		record.InsertID = fmt.Sprintf("INS-%s-%03d", createdAt.Format("20060102"), id)
		record.ID = id
		record.CreatedDatetime = createdAt.Format(time.RFC3339)
		record.LastUpdatedDatetime = updatedAt.Format(time.RFC3339)
		record.Status = status
		record.FraudType = fraudType
		record.RowStatus = rowStatus
		record.FraudStatus = status // Using status as fraud status for now

		if institutionName.Valid {
			record.InstitutionName = institutionName.String
		}
		if institutionType.Valid {
			record.InstitutionType = institutionType.String
		}

		record.InstitutionID = institutionID
		record.Identifiers = []IdentifierInfo{
			{
				Encoded: identifier,
				Type:    identifierType,
			},
		}

		// Set response time if available
		if responseTimeMs.Valid {
			responseTime := int(responseTimeMs.Int64)
			record.ResponseTimeMs = &responseTime
		}

		// Group by association ID
		if associationID.Valid {
			assocID := int(associationID.Int64)
			associationGroups[assocID] = append(associationGroups[assocID], record)
		} else {
			nonAssociatedRecords = append(nonAssociatedRecords, record)
		}
	}

	// Process grouped records
	var inserts []InsertRecord
	
	// Add non-associated records as-is
	inserts = append(inserts, nonAssociatedRecords...)
	
	// Process associated records - create single entries with all identifiers
	for assocID, records := range associationGroups {
		if len(records) == 0 {
			continue
		}
		
		// Use the first record as base
		baseRecord := records[0]
		
		// Collect all unique identifiers from all records in this association
		identifierMap := make(map[string]IdentifierInfo) // key: identifier+type
		for _, record := range records {
			for _, identifier := range record.Identifiers {
				key := identifier.Encoded + ":" + identifier.Type
				identifierMap[key] = identifier
			}
		}
		
		// Convert map back to slice
		var allIdentifiers []IdentifierInfo
		for _, identifier := range identifierMap {
			allIdentifiers = append(allIdentifiers, identifier)
		}
		
		// Update the base record with all identifiers
		baseRecord.Identifiers = allIdentifiers
		
		// Preserve the response time from the first record
		// (The first record already has the response time set correctly)
		
		// Update the insert ID to reflect it's an association
		baseRecord.InsertID = fmt.Sprintf("INS-%s-ASSOC-%d", baseRecord.CreatedDatetime[:10], assocID)
		
		inserts = append(inserts, baseRecord)
	}

	return inserts, total, nil
}

// TaskLogListRequest represents the request parameters for listing task logs
type TaskLogListRequest struct {
	InstitutionID  *int     `json:"institution_id"`
	Status         []string `json:"status"`
	IdentifierType []string `json:"identifier_type"`
	TaskType       []string `json:"task_type"`
	SortBy         string   `json:"sort_by"`
	SortOrder      string   `json:"sort_order"`
	Limit          int      `json:"limit"`
	Offset         int      `json:"offset"`
}

// TaskLogRecord represents a single task log record for the API response
type TaskLogRecord struct {
	TaskID             string                   `json:"taskId"`
	TaskType           string                   `json:"taskType"`
	BlindedIdentifiers []BlindedIdentifier      `json:"blindedIdentifiers"`
	Status             string                   `json:"status"`
	Response           map[string]interface{}   `json:"response"`
	Steps              []map[string]interface{} `json:"steps"`
	Notes              *string                  `json:"notes"`
	Error              *string                  `json:"error"`
	CreatedAt          string                   `json:"createdAt"`
	UpdatedAt          string                   `json:"updatedAt"`
	CreatedBy          string                   `json:"createdBy"`
	QueryCompletedAt   *string                  `json:"queryCompletedAt"`
	StartTime          *string                  `json:"startTime"`
	EndTime            *string                  `json:"endTime"`
}

// ListTaskLogsWithFilters retrieves task log records with filtering and sorting
func (db *DB) ListTaskLogsWithFilters(req TaskLogListRequest) ([]TaskLogRecord, int, error) {
	// Build the base query
	baseQuery := `
		SELECT 
			tl.task_id,
			tl.task_type,
			tl.blinded_identifiers,
			tl.status,
			tl.response,
			tl.steps,
			tl.notes,
			tl.error,
			tl.created_at,
			tl.updated_at,
			tl.created_by,
			tl.query_completed_at,
			tl.start_time,
			tl.end_time
		FROM task_log tl
		WHERE 1=1`

	// Build WHERE clause with filters
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.InstitutionID != nil {
		conditions = append(conditions, fmt.Sprintf("tl.institution_id = $%d", argIndex))
		args = append(args, *req.InstitutionID)
		argIndex++
	}

	if len(req.Status) > 0 {
		placeholders := make([]string, len(req.Status))
		for i := range req.Status {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.Status[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("tl.status IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(req.IdentifierType) > 0 {
		// Filter by identifier types within the blinded_identifiers JSONB array
		identifierConditions := make([]string, len(req.IdentifierType))
		for i, idType := range req.IdentifierType {
			identifierConditions[i] = fmt.Sprintf("tl.blinded_identifiers @> '[{\"type\": \"%s\"}]'", idType)
		}
		conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(identifierConditions, " OR ")))
	}

	if len(req.TaskType) > 0 {
		placeholders := make([]string, len(req.TaskType))
		for i := range req.TaskType {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, req.TaskType[i])
			argIndex++
		}
		conditions = append(conditions, fmt.Sprintf("tl.task_type IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(conditions) > 0 {
		baseQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM (%s) as subquery", baseQuery)
	var total int
	err := db.conn.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Add sorting
	validSortFields := map[string]string{
		"created_at":         "tl.created_at",
		"updated_at":         "tl.updated_at",
		"status":             "tl.status",
		"task_type":          "tl.task_type",
		"query_completed_at": "tl.query_completed_at",
	}

	sortField := validSortFields["created_at"] // default
	if field, exists := validSortFields[req.SortBy]; exists {
		sortField = field
	}

	sortOrder := "DESC"
	if strings.ToUpper(req.SortOrder) == "ASC" {
		sortOrder = "ASC"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortField, sortOrder)

	// Add pagination
	if req.Limit > 0 {
		baseQuery += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, req.Limit)
		argIndex++
	}

	if req.Offset > 0 {
		baseQuery += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, req.Offset)
	}

	// Execute the query
	rows, err := db.conn.Query(baseQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query task logs: %v", err)
	}
	defer rows.Close()

	var taskLogs []TaskLogRecord
	for rows.Next() {
		var record TaskLogRecord
		var taskID uuid.UUID
		var taskType, status, createdBy string
		var notes, error sql.NullString
		var responseJSON, stepsJSON, blindedIdentifiersJSON []byte
		var createdAt, updatedAt time.Time
		var queryCompletedAt, startTime, endTime sql.NullTime

		err := rows.Scan(
			&taskID, &taskType, &blindedIdentifiersJSON, &status,
			&responseJSON, &stepsJSON, &notes, &error, &createdAt, &updatedAt,
			&createdBy, &queryCompletedAt, &startTime, &endTime)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan task log record: %v", err)
		}

		// Parse JSON fields
		var response map[string]interface{}
		if err := json.Unmarshal(responseJSON, &response); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal response: %v", err)
		}

		var steps []map[string]interface{}
		if err := json.Unmarshal(stepsJSON, &steps); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal steps: %v", err)
		}

		var blindedIdentifiers []BlindedIdentifier
		if err := json.Unmarshal(blindedIdentifiersJSON, &blindedIdentifiers); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal blinded identifiers: %v", err)
		}

		// Format the record
		record.TaskID = taskID.String()
		record.TaskType = taskType
		record.BlindedIdentifiers = blindedIdentifiers
		record.Status = status
		record.Response = response
		record.Steps = steps
		record.CreatedAt = createdAt.Format(time.RFC3339)
		record.UpdatedAt = updatedAt.Format(time.RFC3339)
		record.CreatedBy = createdBy

		if notes.Valid {
			record.Notes = &notes.String
		}
		if error.Valid {
			record.Error = &error.String
		}
		if queryCompletedAt.Valid {
			completedAt := queryCompletedAt.Time.Format(time.RFC3339)
			record.QueryCompletedAt = &completedAt
		}
		if startTime.Valid {
			startTimeStr := startTime.Time.Format(time.RFC3339)
			record.StartTime = &startTimeStr
		}
		if endTime.Valid {
			endTimeStr := endTime.Time.Format(time.RFC3339)
			record.EndTime = &endTimeStr
		}

		taskLogs = append(taskLogs, record)
	}

	return taskLogs, total, nil
}

// CreateQueryResponseDetails creates a new query response details record
func (db *DB) CreateQueryResponseDetails(details *QueryResponseDetails) error {
	query := `
		INSERT INTO query_response_details (
			task_id, query_id, institution_id, original_identifiers, blinded_identifiers,
			double_encrypted_values, single_decrypted_values, fraud_data_results,
			response_summary, response_timeline, response_processes, response_stats,
			response_time_ms, status
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
		RETURNING id, created_at, updated_at`

	originalIdentifiersJSON, err := json.Marshal(details.OriginalIdentifiers)
	if err != nil {
		return fmt.Errorf("failed to marshal original identifiers: %v", err)
	}

	blindedIdentifiersJSON, err := json.Marshal(details.BlindedIdentifiers)
	if err != nil {
		return fmt.Errorf("failed to marshal blinded identifiers: %v", err)
	}

	doubleEncryptedValuesJSON, err := json.Marshal(details.DoubleEncryptedValues)
	if err != nil {
		return fmt.Errorf("failed to marshal double encrypted values: %v", err)
	}

	singleDecryptedValuesJSON, err := json.Marshal(details.SingleDecryptedValues)
	if err != nil {
		return fmt.Errorf("failed to marshal single decrypted values: %v", err)
	}

	fraudDataResultsJSON, err := json.Marshal(details.FraudDataResults)
	if err != nil {
		return fmt.Errorf("failed to marshal fraud data results: %v", err)
	}

	responseSummaryJSON, err := json.Marshal(details.ResponseSummary)
	if err != nil {
		return fmt.Errorf("failed to marshal response summary: %v", err)
	}

	responseTimelineJSON, err := json.Marshal(details.ResponseTimeline)
	if err != nil {
		return fmt.Errorf("failed to marshal response timeline: %v", err)
	}

	responseProcessesJSON, err := json.Marshal(details.ResponseProcesses)
	if err != nil {
		return fmt.Errorf("failed to marshal response processes: %v", err)
	}

	responseStatsJSON, err := json.Marshal(details.ResponseStats)
	if err != nil {
		return fmt.Errorf("failed to marshal response stats: %v", err)
	}

	err = db.conn.QueryRow(
		query,
		details.TaskID,
		details.QueryID,
		details.InstitutionID,
		originalIdentifiersJSON,
		blindedIdentifiersJSON,
		doubleEncryptedValuesJSON,
		singleDecryptedValuesJSON,
		fraudDataResultsJSON,
		responseSummaryJSON,
		responseTimelineJSON,
		responseProcessesJSON,
		responseStatsJSON,
		details.ResponseTimeMs,
		details.Status,
	).Scan(&details.ID, &details.CreatedAt, &details.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create query response details: %v", err)
	}

	return nil
}

// GetQueryResponseDetailsByTaskID retrieves query response details by task ID
func (db *DB) GetQueryResponseDetailsByTaskID(taskID uuid.UUID) (*QueryResponseDetails, error) {
	query := `
		SELECT id, task_id, query_id, institution_id, original_identifiers, blinded_identifiers,
		       double_encrypted_values, single_decrypted_values, fraud_data_results,
		       response_summary, response_timeline, response_processes, response_stats,
		       response_time_ms, status, created_at, updated_at
		FROM query_response_details
		WHERE task_id = $1`

	var details QueryResponseDetails
	var originalIdentifiersJSON, blindedIdentifiersJSON, doubleEncryptedValuesJSON,
		singleDecryptedValuesJSON, fraudDataResultsJSON, responseSummaryJSON,
		responseTimelineJSON, responseProcessesJSON, responseStatsJSON []byte

	err := db.conn.QueryRow(query, taskID).Scan(
		&details.ID, &details.TaskID, &details.QueryID, &details.InstitutionID,
		&originalIdentifiersJSON, &blindedIdentifiersJSON, &doubleEncryptedValuesJSON,
		&singleDecryptedValuesJSON, &fraudDataResultsJSON, &responseSummaryJSON,
		&responseTimelineJSON, &responseProcessesJSON, &responseStatsJSON,
		&details.ResponseTimeMs, &details.Status, &details.CreatedAt, &details.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("query response details not found for task ID: %s", taskID)
		}
		return nil, fmt.Errorf("failed to get query response details: %v", err)
	}

	// Unmarshal JSON fields
	if err := json.Unmarshal(originalIdentifiersJSON, &details.OriginalIdentifiers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal original identifiers: %v", err)
	}
	if err := json.Unmarshal(blindedIdentifiersJSON, &details.BlindedIdentifiers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal blinded identifiers: %v", err)
	}
	if err := json.Unmarshal(doubleEncryptedValuesJSON, &details.DoubleEncryptedValues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal double encrypted values: %v", err)
	}
	if err := json.Unmarshal(singleDecryptedValuesJSON, &details.SingleDecryptedValues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal single decrypted values: %v", err)
	}
	if err := json.Unmarshal(fraudDataResultsJSON, &details.FraudDataResults); err != nil {
		return nil, fmt.Errorf("failed to unmarshal fraud data results: %v", err)
	}
	if err := json.Unmarshal(responseSummaryJSON, &details.ResponseSummary); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response summary: %v", err)
	}
	if err := json.Unmarshal(responseTimelineJSON, &details.ResponseTimeline); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response timeline: %v", err)
	}
	if err := json.Unmarshal(responseProcessesJSON, &details.ResponseProcesses); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response processes: %v", err)
	}
	if err := json.Unmarshal(responseStatsJSON, &details.ResponseStats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response stats: %v", err)
	}

	return &details, nil
}

// GetQueryResponseDetailsByQueryID retrieves query response details by query ID
func (db *DB) GetQueryResponseDetailsByQueryID(queryID string) (*QueryResponseDetails, error) {
	query := `
		SELECT id, task_id, query_id, institution_id, original_identifiers, blinded_identifiers,
		       double_encrypted_values, single_decrypted_values, fraud_data_results,
		       response_summary, response_timeline, response_processes, response_stats,
		       response_time_ms, status, created_at, updated_at
		FROM query_response_details
		WHERE query_id = $1`

	var details QueryResponseDetails
	var originalIdentifiersJSON, blindedIdentifiersJSON, doubleEncryptedValuesJSON,
		singleDecryptedValuesJSON, fraudDataResultsJSON, responseSummaryJSON,
		responseTimelineJSON, responseProcessesJSON, responseStatsJSON []byte

	err := db.conn.QueryRow(query, queryID).Scan(
		&details.ID, &details.TaskID, &details.QueryID, &details.InstitutionID,
		&originalIdentifiersJSON, &blindedIdentifiersJSON, &doubleEncryptedValuesJSON,
		&singleDecryptedValuesJSON, &fraudDataResultsJSON, &responseSummaryJSON,
		&responseTimelineJSON, &responseProcessesJSON, &responseStatsJSON,
		&details.ResponseTimeMs, &details.Status, &details.CreatedAt, &details.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("query response details not found for query ID: %s", queryID)
		}
		return nil, fmt.Errorf("failed to get query response details: %v", err)
	}

	// Unmarshal JSON fields
	if err := json.Unmarshal(originalIdentifiersJSON, &details.OriginalIdentifiers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal original identifiers: %v", err)
	}
	if err := json.Unmarshal(blindedIdentifiersJSON, &details.BlindedIdentifiers); err != nil {
		return nil, fmt.Errorf("failed to unmarshal blinded identifiers: %v", err)
	}
	if err := json.Unmarshal(doubleEncryptedValuesJSON, &details.DoubleEncryptedValues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal double encrypted values: %v", err)
	}
	if err := json.Unmarshal(singleDecryptedValuesJSON, &details.SingleDecryptedValues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal single decrypted values: %v", err)
	}
	if err := json.Unmarshal(fraudDataResultsJSON, &details.FraudDataResults); err != nil {
		return nil, fmt.Errorf("failed to unmarshal fraud data results: %v", err)
	}
	if err := json.Unmarshal(responseSummaryJSON, &details.ResponseSummary); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response summary: %v", err)
	}
	if err := json.Unmarshal(responseTimelineJSON, &details.ResponseTimeline); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response timeline: %v", err)
	}
	if err := json.Unmarshal(responseProcessesJSON, &details.ResponseProcesses); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response processes: %v", err)
	}
	if err := json.Unmarshal(responseStatsJSON, &details.ResponseStats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response stats: %v", err)
	}

	return &details, nil
}
