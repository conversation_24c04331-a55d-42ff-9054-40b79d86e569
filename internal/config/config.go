// Package config handles loading and managing company configurations.
package config

import (
	"encoding/hex"
	"fmt"
	"os"
	"strconv"

	"github.com/joho/godotenv"
	"secure_double_blind_ec/internal/types"
	"secure_double_blind_ec/pkg/database"
)

// LoadCompanyConfig loads a company's configuration from environment variables or database
func LoadCompanyConfig(companyID string) (*types.CompanyConfig, error) {
	// Try to load from database first (preferred method)
	db, err := database.New()
	if err == nil {
		defer db.Close()
		
		// Extract institution ID from companyID (e.g., "company_1" -> 1)
		var institutionID int
		if len(companyID) > 8 && companyID[:8] == "company_" {
			institutionID, err = strconv.Atoi(companyID[8:])
			if err != nil {
				// If not a numeric ID, try to get by name
				institution, err := db.GetInstitutionByName(companyID)
				if err == nil {
					institutionID = institution.ID
				}
			}
		}
		
		if institutionID > 0 {
			institution, err := db.GetInstitutionByID(institutionID)
			if err == nil && institution != nil && institution.EncryptionKey != nil && institution.EncryptionSalt != nil {
				key, err := hex.DecodeString(*institution.EncryptionKey)
				if err == nil && len(key) >= 32 {
					salt, err := hex.DecodeString(*institution.EncryptionSalt)
					if err == nil && len(salt) >= 32 {
						return &types.CompanyConfig{
							ID:   companyID,
							Key:  key[:32], // Use first 32 bytes
							Salt: salt[:32], // Use first 32 bytes
						}, nil
					}
				}
			}
		}
	}
	
	// Fallback to environment variables if database lookup fails
	var keyHex, saltHex string
	
	// Try company-specific environment variables first (for central services)
	switch companyID {
	case "company_a":
		keyHex = os.Getenv("COMPANY_A_KEY")
		saltHex = os.Getenv("COMPANY_A_SALT")
	case "company_b":
		keyHex = os.Getenv("COMPANY_B_KEY")
		saltHex = os.Getenv("COMPANY_B_SALT")
	case "company_c":
		keyHex = os.Getenv("COMPANY_C_KEY")
		saltHex = os.Getenv("COMPANY_C_SALT")
	}
	
	// If company-specific vars not found, try standard vars (for individual company services)
	if keyHex == "" || saltHex == "" {
		keyHex = os.Getenv("COMPANY_KEY")
		saltHex = os.Getenv("COMPANY_SALT")
	}
	
	if keyHex == "" || saltHex == "" {
		return nil, fmt.Errorf("missing encryption keys for company %s in both database and environment variables", companyID)
	}
	
	key, err := hex.DecodeString(keyHex)
	if err != nil {
		return nil, fmt.Errorf("invalid key format for company %s: %v", companyID, err)
	}
	
	salt, err := hex.DecodeString(saltHex)
	if err != nil {
		return nil, fmt.Errorf("invalid salt format for company %s: %v", companyID, err)
	}
	
	if len(key) < 32 || len(salt) < 32 {
		return nil, fmt.Errorf("key and salt must be at least 32 bytes for company %s", companyID)
	}
	
	return &types.CompanyConfig{
		ID:   companyID,
		Key:  key[:32], // Use first 32 bytes
		Salt: salt[:32], // Use first 32 bytes
	}, nil
}

// LoadAllCompanyConfigs loads configurations for all supported companies
func LoadAllCompanyConfigs() (map[string]*types.CompanyConfig, error) {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		// Don't fail if .env file is missing, we'll try database
	}
	
	// Try to load from database first
	db, err := database.New()
	if err == nil {
		defer db.Close()
		
		institutions, err := db.ListInstitutions()
		if err == nil && len(institutions) > 0 {
			companies := make(map[string]*types.CompanyConfig)
			
			for _, institution := range institutions {
				if institution.EncryptionKey != nil && institution.EncryptionSalt != nil {
					key, err := hex.DecodeString(*institution.EncryptionKey)
					if err == nil && len(key) >= 32 {
						salt, err := hex.DecodeString(*institution.EncryptionSalt)
						if err == nil && len(salt) >= 32 {
							companyID := fmt.Sprintf("company_%d", institution.ID)
							companies[companyID] = &types.CompanyConfig{
								ID:   companyID,
								Key:  key[:32],
								Salt: salt[:32],
							}
						}
					}
				}
			}
			
			if len(companies) > 0 {
				return companies, nil
			}
		}
	}
	
	// Fallback to environment variables
	companies := make(map[string]*types.CompanyConfig)
	companyIDs := []string{"company_a", "company_b", "company_c"}
	
	for _, companyID := range companyIDs {
		config, err := LoadCompanyConfig(companyID)
		if err != nil {
			return nil, fmt.Errorf("failed to load config for %s: %v", companyID, err)
		}
		companies[companyID] = config
	}
	
	return companies, nil
}
