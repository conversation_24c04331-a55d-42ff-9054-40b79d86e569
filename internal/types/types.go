// Package types defines the core data structures used throughout the secure double-blind system.
package types

// CompanyConfig holds the cryptographic configuration for a specific company
type CompanyConfig struct {
	ID   string
	Key  []byte // 32-byte company-specific key
	Salt []byte // 32-byte company-specific salt
}

// StoredRecord represents a record in the main server's database
type StoredRecord struct {
	BlindedID       string            `json:"blinded_id"`       // Company's own blinded ID
	OwnerCompany    string            `json:"owner_company"`    // Company that stored this record
	EncryptedData   []byte            `json:"encrypted_data"`   // Company's encrypted data
	Metadata        map[string]string `json:"metadata"`         // Public metadata
	StoredAt        string            `json:"stored_at"`        // Timestamp
}
