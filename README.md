# SMS Parser for Indian Financial Notifications

A comprehensive Python library for extracting structured financial data from Indian SMS notifications using regex-based extraction logic. This parser can handle various types of financial SMS including transactions, payments, salary credits, account status updates, and more.

## Features

- **Multi-pattern Extraction**: Uses multiple regex patterns per field for robust matching
- **Frequency-based Selection**: Selects the most confident match based on pattern frequency
- **Comprehensive Classification**: Classifies SMS into types, subtypes, and information categories
- **Async Support**: Built with async/await for better performance
- **Extensive Field Support**: Extracts 20+ different financial fields
- **Indian Banking Focus**: Optimized for Indian banking SMS formats and terminology

## Supported SMS Types

### Purchase
- **UPI Transactions**: Amount, recipient, transaction reference, account details
- **Debit Card**: Amount, merchant, card number, transaction reference

### Payment
- **EMI Payments**: EMI amount, number, due date, interest, late fees

### Deposit & Withdrawal
- **Loan Disbursal**: Loan ID, amount, lender details
- **Monthly Salary Credit**: Employer name, salary amount

### Accounts
- **Bank Account Status**: Balance, account status, account type
- **Loan Account**: Outstanding amount, default status, loan details

### Investment
- **SIP/Mutual Funds**: Investment amount, fund details, units

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

```python
import asyncio
from sms_parser import SMSParser

# Initialize parser
parser = SMSParser()

# Sample SMS
sms_text = """UPI txn of Rs 180.0 debited from A/c X4884 on 02May24 to Dheeraj Neeraj J. 
Ref no ************. Avl bal Rs 2000."""

# Parse SMS (async)
async def parse_example():
    results = await parser.parse_sms(sms_text)
    return results

# Run parser
results = asyncio.run(parse_example())
print(results)
```

### Synchronous Usage

```python
from sms_parser import parse_sms_sync

# Parse SMS synchronously
results = parse_sms_sync(sms_text)
print(results)
```

## Output Format

The parser returns a list of JSON objects, each representing a distinct financial event:

```json
[
  {
    "sms_type": "Purchase",
    "sms_event_subtype": "UPI",
    "sms_info_type": "Outflow",
    "account_number": "X4884",
    "amount": "180.0",
    "date": "02-05-2024",
    "upi_recipient": "Dheeraj Neeraj J",
    "txn_ref": "************",
    "currency": "INR"
  }
]
```

## Field Hierarchy

### Common Fields
- `amount`: Transaction amount
- `date`: Transaction date (DD-MM-YYYY format)
- `account_number`: Account number (may be masked)
- `bank_name`: Bank name
- `txn_ref`: Transaction reference number
- `currency`: Currency code (default: INR)

### UPI Specific
- `upi_recipient`: Recipient name

### Card Specific
- `card_number`: Card number (masked)
- `merchant_name`: Merchant name

### EMI Specific
- `emi_amount`: EMI amount
- `emi_number`: EMI installment number
- `due_date`: Due date
- `interest_charged`: Interest amount
- `late_fee_charged`: Late fee amount
- `is_loan_repayment`: Boolean flag
- `is_loan_delayed`: Boolean flag

### Loan Specific
- `loan_id`: Loan identifier
- `lender_name`: Lender name
- `current_amount`: Current outstanding amount
- `default_status`: Default status

### Account Specific
- `account_status`: Account status (Active/Inactive/Closed)
- `account_sub_type`: Account type (Savings/Current/Salary)
- `account_opening_date`: Account opening date
- `current_amount`: Current balance

## Architecture

### Core Components

1. **SMSParser**: Main parser class that orchestrates the extraction process
2. **FieldExtractor**: Handles field-specific extraction using multiple regex patterns
3. **SMSClassifier**: Classifies SMS into types and subtypes
4. **Utils**: Utility functions for data cleaning and normalization

### Extraction Strategy

1. **Text Cleaning**: Normalize SMS text and remove noise
2. **Segmentation**: Split SMS into segments for multi-event detection
3. **Classification**: Determine SMS type, event subtype, and info type
4. **Field Extraction**: Extract relevant fields using multiple regex patterns
5. **Validation**: Validate and normalize extracted data
6. **Frequency Selection**: Choose most frequent/confident matches

## Testing

Run the test suite to see the parser in action:

```bash
python test_parser.py
```

This will test the parser with various sample SMS messages and show the extraction results.

## Customization

### Adding New Patterns

To add new regex patterns for existing fields, modify the patterns in `field_extractors.py`:

```python
# Add new amount pattern
self.amount_patterns.append(
    re.compile(r"new_pattern_here", re.IGNORECASE)
)
```

### Adding New Fields

1. Add extraction method in `FieldExtractor`
2. Update the relevant extraction method in `SMSParser`
3. Add classification rules if needed in `SMSClassifier`

### Adding New SMS Types

1. Add classification rules in `classifiers.py`
2. Add corresponding extraction methods in `sms_parser.py`
3. Update field hierarchy documentation

## Performance

- **Async Support**: Built for concurrent processing of multiple SMS
- **Compiled Patterns**: Regex patterns are pre-compiled for better performance
- **Efficient Matching**: Uses frequency-based selection to avoid over-processing

## Error Handling

The parser is designed to be robust:
- Invalid SMS text returns empty list
- Failed field extraction returns None for that field
- Classification fallbacks to 'Other' for unrecognized patterns
- Graceful handling of malformed data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions, please create an issue in the repository.
