#!/usr/bin/env python3
"""
Script to compare hybrid processing results with original rule-based results.
"""

import csv
import json
from collections import defaultdict

def load_csv_data(filename):
    """Load CSV data into a list of dictionaries."""
    data = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
    except FileNotFoundError:
        print(f"File {filename} not found.")
        return []
    return data

def compare_classifications(original_data, hybrid_data):
    """Compare classification results between original and hybrid approaches."""
    
    print("CLASSIFICATION COMPARISON")
    print("=" * 50)
    
    # Create lookup by original_id
    original_lookup = {row['original_id']: row for row in original_data}
    hybrid_lookup = {row['original_id']: row for row in hybrid_data}
    
    # Find common IDs
    common_ids = set(original_lookup.keys()) & set(hybrid_lookup.keys())
    
    print(f"Original records: {len(original_data)}")
    print(f"Hybrid records: {len(hybrid_data)}")
    print(f"Common records: {len(common_ids)}")
    print()
    
    # Classification comparison
    classification_changes = defaultdict(int)
    financial_improvements = []
    non_financial_improvements = []
    
    for msg_id in common_ids:
        orig = original_lookup[msg_id]
        hybrid = hybrid_lookup[msg_id]
        
        orig_class = orig.get('classification', 'unknown')
        hybrid_class = hybrid.get('classification', 'unknown')
        
        if orig_class != hybrid_class:
            classification_changes[f"{orig_class} -> {hybrid_class}"] += 1
            
            # Track specific improvements
            if orig_class == 'non-financial' and hybrid_class == 'financial':
                financial_improvements.append({
                    'id': msg_id,
                    'text': hybrid.get('original_text', '')[:100],
                    'original_type': orig.get('sms_type', ''),
                    'hybrid_type': hybrid.get('sms_type', ''),
                    'amount': hybrid.get('amount', '')
                })
            elif orig_class == 'financial' and hybrid_class == 'non-financial':
                non_financial_improvements.append({
                    'id': msg_id,
                    'text': hybrid.get('original_text', '')[:100],
                    'original_type': orig.get('sms_type', ''),
                    'hybrid_type': hybrid.get('sms_type', '')
                })
    
    print("Classification Changes:")
    for change, count in classification_changes.items():
        print(f"  {change}: {count}")
    print()
    
    # Show examples of improvements
    if financial_improvements:
        print(f"Messages reclassified as FINANCIAL ({len(financial_improvements)}):")
        for i, msg in enumerate(financial_improvements[:5], 1):
            print(f"  {i}. {msg['text']}... (Amount: {msg['amount']})")
        if len(financial_improvements) > 5:
            print(f"  ... and {len(financial_improvements) - 5} more")
        print()
    
    if non_financial_improvements:
        print(f"Messages reclassified as NON-FINANCIAL ({len(non_financial_improvements)}):")
        for i, msg in enumerate(non_financial_improvements[:5], 1):
            print(f"  {i}. {msg['text']}...")
        if len(non_financial_improvements) > 5:
            print(f"  ... and {len(non_financial_improvements) - 5} more")
        print()

def compare_field_extraction(original_data, hybrid_data):
    """Compare field extraction quality between approaches."""
    
    print("FIELD EXTRACTION COMPARISON")
    print("=" * 50)
    
    # Create lookup by original_id
    original_lookup = {row['original_id']: row for row in original_data}
    hybrid_lookup = {row['original_id']: row for row in hybrid_data}
    
    # Find financial messages in both datasets
    common_financial = []
    for msg_id in set(original_lookup.keys()) & set(hybrid_lookup.keys()):
        orig = original_lookup[msg_id]
        hybrid = hybrid_lookup[msg_id]
        
        if (orig.get('classification') == 'financial' and 
            hybrid.get('classification') == 'financial'):
            common_financial.append(msg_id)
    
    print(f"Common financial messages: {len(common_financial)}")
    
    # Compare key fields
    field_improvements = {
        'amount': 0,
        'entity_name': 0,
        'account_number': 0,
        'txn_ref': 0
    }
    
    examples = []
    
    for msg_id in common_financial[:100]:  # Sample first 100 for analysis
        orig = original_lookup[msg_id]
        hybrid = hybrid_lookup[msg_id]
        
        improvements = []
        
        # Check amount extraction
        orig_amount = orig.get('amount', '').strip()
        hybrid_amount = hybrid.get('amount', '').strip()
        if not orig_amount and hybrid_amount:
            field_improvements['amount'] += 1
            improvements.append(f"amount: '{hybrid_amount}'")
        
        # Check entity extraction
        orig_entity = orig.get('bank_name', '').strip()
        hybrid_entity = hybrid.get('entity_name', '').strip()
        if not orig_entity and hybrid_entity:
            field_improvements['entity_name'] += 1
            improvements.append(f"entity: '{hybrid_entity}'")
        
        # Check account number
        orig_account = orig.get('account_number', '').strip()
        hybrid_account = hybrid.get('account_number', '').strip()
        if not orig_account and hybrid_account:
            field_improvements['account_number'] += 1
            improvements.append(f"account: '{hybrid_account}'")
        
        # Check transaction reference
        orig_ref = orig.get('txn_ref', '').strip()
        hybrid_ref = hybrid.get('txn_ref', '').strip()
        if not orig_ref and hybrid_ref:
            field_improvements['txn_ref'] += 1
            improvements.append(f"txn_ref: '{hybrid_ref}'")
        
        if improvements and len(examples) < 5:
            examples.append({
                'text': hybrid.get('original_text', '')[:80],
                'improvements': improvements
            })
    
    print("\nField Extraction Improvements:")
    for field, count in field_improvements.items():
        print(f"  {field}: +{count} extractions")
    print()
    
    if examples:
        print("Examples of improved extractions:")
        for i, example in enumerate(examples, 1):
            print(f"  {i}. {example['text']}...")
            for improvement in example['improvements']:
                print(f"     + {improvement}")
        print()

def generate_summary_stats(hybrid_data):
    """Generate summary statistics for hybrid results."""
    
    print("HYBRID PROCESSING SUMMARY")
    print("=" * 50)
    
    total = len(hybrid_data)
    financial = sum(1 for row in hybrid_data if row.get('classification') == 'financial')
    non_financial = sum(1 for row in hybrid_data if row.get('classification') == 'non-financial')
    errors = sum(1 for row in hybrid_data if row.get('processing_status') == 'error')
    
    print(f"Total messages: {total}")
    print(f"Financial: {financial} ({financial/total*100:.1f}%)")
    print(f"Non-financial: {non_financial} ({non_financial/total*100:.1f}%)")
    print(f"Errors: {errors} ({errors/total*100:.1f}%)")
    print()
    
    # OpenAI extraction stats
    openai_extractions = sum(1 for row in hybrid_data 
                           if row.get('extracted_data_json', '{}') != '{}' and 
                              row.get('classification') == 'financial')
    
    print(f"Messages with OpenAI extraction: {openai_extractions}")
    print(f"OpenAI extraction rate: {openai_extractions/financial*100:.1f}% of financial messages")

def main():
    """Main comparison function."""
    
    print("SMS PROCESSING RESULTS COMPARISON")
    print("=" * 60)
    print()
    
    # Load data
    original_data = load_csv_data("sms_processed_results.csv")
    hybrid_data = load_csv_data("hybrid_sms_processed_results.csv")
    
    if not original_data:
        print("Could not load original results. Skipping comparison.")
        if hybrid_data:
            generate_summary_stats(hybrid_data)
        return
    
    if not hybrid_data:
        print("Could not load hybrid results. Please run hybrid processing first.")
        return
    
    # Run comparisons
    compare_classifications(original_data, hybrid_data)
    compare_field_extraction(original_data, hybrid_data)
    generate_summary_stats(hybrid_data)
    
    print("Comparison complete!")
    print("\nFiles analyzed:")
    print(f"  Original: sms_processed_results.csv ({len(original_data)} records)")
    print(f"  Hybrid: hybrid_sms_processed_results.csv ({len(hybrid_data)} records)")

if __name__ == "__main__":
    main()
