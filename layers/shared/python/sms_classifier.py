"""
SMS Classification utilities for the processing pipeline
"""
import re
import logging
from typing import Dict, List, Tuple, Any

logger = logging.getLogger(__name__)

def clean_text(text: str) -> str:
    """Clean and normalize text for processing"""
    if not text:
        return ""
    
    # Convert to lowercase and strip whitespace
    text = text.lower().strip()
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters but keep basic punctuation
    text = re.sub(r'[^\w\s\.\,\-\:\;\(\)\[\]\/\@\#\$\%\&\*\+\=\<\>\?\!\~\`\'\"]', ' ', text)
    
    return text

class SMSClassifier:
    """
    SMS classification logic to determine if SMS is financial or non-financial
    """
    
    def __init__(self):
        self._setup_classification_rules()
    
    def _setup_classification_rules(self):
        """Setup classification rules with keywords and patterns"""
        
        # Financial keywords and patterns
        self.financial_keywords = [
            # Banking terms
            'bank', 'account', 'balance', 'transaction', 'transfer', 'deposit', 'withdrawal',
            'credit', 'debit', 'payment', 'amount', 'rupees', 'rs', 'inr',
            
            # Payment methods
            'upi', 'card', 'atm', 'pos', 'neft', 'rtgs', 'imps', 'paytm', 'phonepe', 
            'gpay', 'google pay', 'bhim', 'paypal',
            
            # Financial institutions
            'sbi', 'hdfc', 'icici', 'axis', 'kotak', 'pnb', 'bob', 'canara',
            
            # Transaction types
            'purchase', 'refund', 'cashback', 'reward', 'loan', 'emi', 'insurance',
            'investment', 'mutual fund', 'fd', 'rd', 'savings',
            
            # Security
            'otp', 'pin', 'cvv', 'security code', 'verification',
            
            # Bills and utilities
            'bill', 'recharge', 'electricity', 'gas', 'water', 'mobile', 'broadband'
        ]
        
        self.financial_patterns = [
            # Amount patterns
            r'rs\.?\s*\d+',
            r'inr\s*\d+',
            r'amount.*\d+',
            r'\d+\.\d{2}',
            
            # Account patterns
            r'a/c\s*\w+',
            r'account.*\w{4}',
            r'card.*\w{4}',
            
            # Transaction patterns
            r'txn\s*\w+',
            r'ref\s*no',
            r'transaction.*\w+',
            
            # UPI patterns
            r'upi\s+(?:txn|transaction|payment)',
            r'paid\s+(?:via|through|using)\s+upi',
            
            # Banking patterns
            r'(?:debited|credited)\s+by\s+rs',
            r'available\s+balance',
            r'minimum\s+balance'
        ]
        
        # Non-financial keywords (promotional, informational, etc.)
        self.non_financial_keywords = [
            'offer', 'discount', 'sale', 'free', 'win', 'congratulations',
            'lucky', 'prize', 'gift', 'bonus', 'cashback offer',
            'weather', 'news', 'update', 'reminder', 'appointment',
            'delivery', 'order', 'booking', 'confirmation',
            'birthday', 'anniversary', 'festival', 'holiday'
        ]
    
    def classify_message(self, message_text: str) -> Dict[str, Any]:
        """
        Classify SMS message as financial or non-financial
        
        Returns:
            Dict containing classification results
        """
        if not message_text:
            return {
                'message_type': 'non-financial',
                'confidence': 0.0,
                'matched_keywords': [],
                'matched_patterns': [],
                'reasoning': 'Empty message'
            }
        
        cleaned_text = clean_text(message_text)
        
        # Check for financial keywords
        financial_keyword_matches = []
        for keyword in self.financial_keywords:
            if keyword in cleaned_text:
                financial_keyword_matches.append(keyword)
        
        # Check for financial patterns
        financial_pattern_matches = []
        for pattern in self.financial_patterns:
            if re.search(pattern, cleaned_text):
                financial_pattern_matches.append(pattern)
        
        # Check for non-financial keywords
        non_financial_keyword_matches = []
        for keyword in self.non_financial_keywords:
            if keyword in cleaned_text:
                non_financial_keyword_matches.append(keyword)
        
        # Calculate scores
        financial_score = len(financial_keyword_matches) + len(financial_pattern_matches)
        non_financial_score = len(non_financial_keyword_matches)
        
        # Determine classification
        if financial_score > non_financial_score and financial_score > 0:
            message_type = 'financial'
            confidence = min(financial_score / (financial_score + non_financial_score + 1), 0.95)
            reasoning = f"Found {financial_score} financial indicators"
        elif non_financial_score > 0:
            message_type = 'non-financial'
            confidence = min(non_financial_score / (financial_score + non_financial_score + 1), 0.95)
            reasoning = f"Found {non_financial_score} non-financial indicators"
        else:
            # Default to non-financial if no clear indicators
            message_type = 'non-financial'
            confidence = 0.3
            reasoning = "No clear financial indicators found"
        
        return {
            'message_type': message_type,
            'confidence': confidence,
            'financial_keywords': financial_keyword_matches,
            'financial_patterns': financial_pattern_matches,
            'non_financial_keywords': non_financial_keyword_matches,
            'financial_score': financial_score,
            'non_financial_score': non_financial_score,
            'reasoning': reasoning
        }
    
    def batch_classify(self, messages: List[str]) -> List[Dict[str, Any]]:
        """Classify multiple messages"""
        return [self.classify_message(msg) for msg in messages]

# Global classifier instance
sms_classifier = SMSClassifier()
