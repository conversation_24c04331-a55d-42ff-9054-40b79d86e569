"""
Utility functions for SMS processing pipeline
"""
import json
import logging
import os
import boto3
import csv
from typing import List, Dict, Any, Optional
from io import StringIO
import asyncio
import aiohttp
from datetime import datetime

logger = logging.getLogger(__name__)

# AWS clients
s3_client = boto3.client('s3')
sqs_client = boto3.client('sqs')

def setup_logging(level=logging.INFO):
    """Setup logging configuration"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def parse_s3_path(s3_path: str) -> tuple:
    """Parse S3 path into bucket and key"""
    if not s3_path.startswith('s3://'):
        raise ValueError(f"Invalid S3 path format: {s3_path}")
    
    path_parts = s3_path[5:].split('/', 1)
    if len(path_parts) != 2:
        raise ValueError(f"Invalid S3 path format: {s3_path}")
    
    return path_parts[0], path_parts[1]

def read_file_from_s3(s3_path: str) -> str:
    """Read file content from S3"""
    try:
        bucket, key = parse_s3_path(s3_path)
        response = s3_client.get_object(Bucket=bucket, Key=key)
        content = response['Body'].read().decode('utf-8')
        logger.info(f"Successfully read file from S3: {s3_path}")
        return content
    except Exception as e:
        logger.error(f"Failed to read file from S3 {s3_path}: {str(e)}")
        raise

def parse_sms_file(file_content: str, file_format: str = 'csv') -> List[Dict[str, str]]:
    """Parse SMS file content into list of messages"""
    messages = []
    
    try:
        if file_format.lower() == 'csv':
            csv_reader = csv.DictReader(StringIO(file_content))
            for row in csv_reader:
                # Assume CSV has columns: message_text, sender, timestamp, etc.
                if 'message' in row or 'text' in row or 'body' in row:
                    message_text = row.get('message') or row.get('text') or row.get('body')
                    if message_text:
                        messages.append({
                            'text': message_text.strip(),
                            'sender': row.get('sender', ''),
                            'timestamp': row.get('timestamp', ''),
                            'raw_data': row
                        })
        
        elif file_format.lower() == 'json':
            data = json.loads(file_content)
            if isinstance(data, list):
                for item in data:
                    if isinstance(item, dict) and ('message' in item or 'text' in item):
                        message_text = item.get('message') or item.get('text')
                        if message_text:
                            messages.append({
                                'text': message_text.strip(),
                                'sender': item.get('sender', ''),
                                'timestamp': item.get('timestamp', ''),
                                'raw_data': item
                            })
        
        else:
            # Treat as plain text, each line is a message
            lines = file_content.strip().split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    messages.append({
                        'text': line.strip(),
                        'sender': '',
                        'timestamp': '',
                        'raw_data': {'line_number': i + 1}
                    })
        
        logger.info(f"Parsed {len(messages)} messages from file")
        return messages
    
    except Exception as e:
        logger.error(f"Failed to parse SMS file: {str(e)}")
        raise

def send_sqs_message(queue_url: str, message_body: Dict[str, Any], 
                    delay_seconds: int = 0) -> str:
    """Send message to SQS queue"""
    try:
        response = sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps(message_body),
            DelaySeconds=delay_seconds
        )
        message_id = response['MessageId']
        logger.info(f"Sent message to SQS queue {queue_url}: {message_id}")
        return message_id
    except Exception as e:
        logger.error(f"Failed to send message to SQS queue {queue_url}: {str(e)}")
        raise

def batch_list(items: List[Any], batch_size: int) -> List[List[Any]]:
    """Split list into batches of specified size"""
    batches = []
    for i in range(0, len(items), batch_size):
        batches.append(items[i:i + batch_size])
    return batches

async def call_openai_api(messages: List[str], api_key: str) -> List[Dict[str, Any]]:
    """Call OpenAI API to analyze messages"""
    if not messages:
        return []
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    results = []
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for message in messages:
            task = analyze_single_message(session, message, headers)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results and handle exceptions
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"OpenAI API call failed for message {i}: {str(result)}")
            processed_results.append({
                'message': messages[i],
                'analysis': None,
                'error': str(result),
                'success': False
            })
        else:
            processed_results.append({
                'message': messages[i],
                'analysis': result,
                'error': None,
                'success': True
            })
    
    return processed_results

async def analyze_single_message(session: aiohttp.ClientSession, message: str, 
                                headers: Dict[str, str]) -> Dict[str, Any]:
    """Analyze a single message using OpenAI API"""
    
    prompt = f"""
    Analyze the following SMS message and provide a detailed analysis:
    
    Message: "{message}"
    
    Please provide:
    1. Message category (financial, promotional, informational, transactional, etc.)
    2. Sentiment (positive, negative, neutral)
    3. Key entities mentioned (amounts, dates, organizations, etc.)
    4. Intent or purpose of the message
    5. Risk level (low, medium, high) if applicable
    6. Any suspicious patterns or red flags
    
    Respond in JSON format.
    """
    
    payload = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "system", "content": "You are an expert SMS analyzer. Provide detailed analysis in JSON format."},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 500,
        "temperature": 0.3
    }
    
    async with session.post(
        'https://api.openai.com/v1/chat/completions',
        headers=headers,
        json=payload
    ) as response:
        if response.status == 200:
            data = await response.json()
            content = data['choices'][0]['message']['content']
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                return {'raw_response': content, 'parsed': False}
        else:
            error_text = await response.text()
            raise Exception(f"OpenAI API error {response.status}: {error_text}")

async def call_external_api(endpoint: str, customer_id: str, 
                          additional_data: Dict[str, Any] = None) -> bool:
    """Call external API to signal completion"""
    try:
        payload = {
            'customer_id': customer_id,
            'status': 'completed',
            'timestamp': datetime.utcnow().isoformat(),
            'processing_complete': True
        }
        
        if additional_data:
            payload.update(additional_data)
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'SMS-Processing-Pipeline/1.0'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, json=payload, headers=headers) as response:
                if response.status in [200, 201, 202]:
                    logger.info(f"Successfully notified external API for customer {customer_id}")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"External API call failed {response.status}: {error_text}")
                    return False
    
    except Exception as e:
        logger.error(f"Failed to call external API: {str(e)}")
        return False

def get_environment_variable(key: str, default: Any = None, required: bool = False) -> Any:
    """Get environment variable with optional default and required check"""
    value = os.environ.get(key, default)
    
    if required and value is None:
        raise ValueError(f"Required environment variable {key} is not set")
    
    return value

def validate_message_format(message: Dict[str, Any]) -> bool:
    """Validate SQS message format"""
    required_fields = ['customer_id']
    
    for field in required_fields:
        if field not in message:
            logger.error(f"Missing required field: {field}")
            return False
    
    return True

def create_error_response(error_message: str, error_code: str = "PROCESSING_ERROR") -> Dict[str, Any]:
    """Create standardized error response"""
    return {
        'success': False,
        'error': {
            'code': error_code,
            'message': error_message,
            'timestamp': datetime.utcnow().isoformat()
        }
    }

def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response"""
    response = {
        'success': True,
        'message': message,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if data is not None:
        response['data'] = data
    
    return response
