#!/bin/bash

# Test script for the company-list-task-logs API
# Replace with your actual API Gateway URL
BASE_URL="https://94gnwh0cr9.execute-api.ap-south-1.amazonaws.com/dev"

# Test institution ID (replace with actual institution ID)
INSTITUTION_ID="1"

echo "Testing company-list-task-logs API..."
echo "======================================"

# Test 1: Basic request
echo "Test 1: Basic request"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 2: With status filter
echo "Test 2: With status filter"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?status=completed,failed" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 3: With identifier type filter
echo "Test 3: With identifier type filter"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?identifier_type=email,phone" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 4: With task type filter
echo "Test 4: With task type filter"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?task_type=query" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 5: With pagination
echo "Test 5: With pagination"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?limit=5&offset=0" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 6: With sorting
echo "Test 6: With sorting"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?sort_by=created_at&sort_order=desc" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

# Test 7: Combined filters
echo "Test 7: Combined filters"
curl -X GET "${BASE_URL}/institution/${INSTITUTION_ID}/task-logs?status=completed&identifier_type=email&limit=3&sort_by=created_at&sort_order=desc" \
  -H "Content-Type: application/json" \
  | jq '.'

echo -e "\n\n"

echo "API testing completed!" 