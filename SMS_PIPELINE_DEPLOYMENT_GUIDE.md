# SMS Processing Pipeline - Deployment Guide

## Overview

This directory contains a complete SAM serverless data processing pipeline for SMS message analysis. The pipeline processes SMS files from S3, classifies them as financial/non-financial, analyzes them using OpenAI API, and notifies external systems when complete.

## Architecture

```
External Service → SQS Input Queue → Lambda 1 (SMS Segregation) → SQS Batch Queue → Lambda 2 (Message Analysis) → SQS Completion Queue → Lambda 3 (Completion Check) → External API
```

## Components in This Directory

### 1. AWS Infrastructure
- **`template.yaml`** - SAM template defining all AWS resources
- **`samconfig.toml`** - SAM deployment configuration
- **`schema.sql`** - PostgreSQL database schema for `customer_investigation_kb`

### 2. Lambda Functions
- **`lambdas/sms-segregation/`** - Reads S3 files, classifies messages, creates batches
- **`lambdas/message-analysis/`** - Analyzes messages with OpenAI API
- **`lambdas/completion-check/`** - Checks completion and calls external API

### 3. Shared Layer
- **`layers/shared/python/`** - Common utilities for database, classification, and API calls

### 4. Build & Deployment
- **`Makefile`** - Build automation and deployment commands
- **`deploy.sh`** - Automated deployment script

## Quick Start

### 1. Prerequisites
```bash
# Install AWS SAM CLI
pip install aws-sam-cli

# Install project dependencies
pip install -r pipeline-requirements.txt

# Configure AWS CLI
aws configure
```

### 2. Environment Setup
```bash
# Create environment file
cp .env.example .env

# Edit .env with your actual values:
# - Database credentials
# - OpenAI API key
# - External API endpoint
```

### 3. Database Setup
```bash
# Initialize database schema
psql -h your-db-host -U postgres -d postgres -f schema.sql
```

### 4. Deploy Pipeline
```bash
# Option 1: Guided deployment (recommended for first time)
./deploy.sh --guided --init-db

# Option 2: Automated deployment
make deploy

# Option 3: Manual SAM deployment
sam build
sam deploy --guided
```

## Configuration

### Environment Variables (in samconfig.toml)

Update the `parameter_overrides` in `samconfig.toml`:

```toml
parameter_overrides = "Environment=\"dev\" DatabaseHost=\"your-db-host\" DatabasePassword=\"your-password\" OpenAIApiKey=\"your-openai-key\" ExternalApiEndpoint=\"https://your-api.com/webhook\""
```

### Key Parameters:
- **DatabaseHost**: Your PostgreSQL RDS endpoint
- **DatabasePassword**: Database password
- **DatabaseSchema**: `customer_investigation_kb` (default)
- **BatchSize**: `50` (default, messages per batch)
- **OpenAIApiKey**: Your OpenAI API key
- **ExternalApiEndpoint**: Webhook URL for completion notifications

## Usage

### 1. Trigger Processing

Send a message to the SMS Input Queue:

```json
{
  "s3_path": "s3://your-bucket/path/to/sms-file.csv",
  "customer_id": "customer_123"
}
```

### 2. Supported File Formats

- **CSV**: Columns named `message`, `text`, or `body`
- **JSON**: Array with `message` or `text` fields
- **TXT**: Each line is a separate message

### 3. Monitor Progress

- **CloudWatch Logs**: Check Lambda function logs
- **Database**: Query `customer_processing_status` table
- **SQS**: Monitor queue depths and DLQ messages

## Testing

### 1. Run Unit Tests
```bash
make test
```

### 2. Test with Sample Data
```bash
# Create a test CSV file
echo "message" > test_sms.csv
echo "Your account debited by Rs 1000" >> test_sms.csv
echo "Happy birthday!" >> test_sms.csv

# Upload to S3
aws s3 cp test_sms.csv s3://your-bucket/test/

# Send test message to SQS
aws sqs send-message \
  --queue-url $(aws cloudformation describe-stacks \
    --stack-name sms-processing-pipeline \
    --query 'Stacks[0].Outputs[?OutputKey==`SMSInputQueueUrl`].OutputValue' \
    --output text) \
  --message-body '{"s3_path":"s3://your-bucket/test/test_sms.csv","customer_id":"test_123"}'
```

## Monitoring

### CloudWatch Logs
- `/aws/lambda/sms-processing-pipeline-SMSSegregationFunction-*`
- `/aws/lambda/sms-processing-pipeline-MessageAnalysisFunction-*`
- `/aws/lambda/sms-processing-pipeline-CompletionCheckFunction-*`

### Database Monitoring
```sql
-- Check customer processing status
SELECT * FROM customer_investigation_kb.customer_processing_status;

-- Check processing logs
SELECT * FROM customer_investigation_kb.processing_logs 
ORDER BY created_at DESC LIMIT 10;

-- Check message processing
SELECT customer_id, message_type, processed, COUNT(*) 
FROM customer_investigation_kb.sms_messages 
GROUP BY customer_id, message_type, processed;
```

### SQS Monitoring
- Monitor queue depths in AWS Console
- Check Dead Letter Queues for failed messages
- Set up CloudWatch alarms for queue metrics

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check security groups and VPC configuration
   - Verify database credentials in environment variables

2. **OpenAI API Errors**
   - Verify API key is correct and has sufficient credits
   - Check rate limits and quotas

3. **S3 Access Errors**
   - Verify Lambda execution role has S3 read permissions
   - Check bucket policies and IAM roles

### Debug Commands
```bash
# Check stack status
aws cloudformation describe-stacks --stack-name sms-processing-pipeline

# View recent logs
sam logs -n SMSSegregationFunction --tail

# Check queue attributes
aws sqs get-queue-attributes --queue-url <queue-url> --attribute-names All
```

## Cleanup

To remove all resources:
```bash
# Delete CloudFormation stack
aws cloudformation delete-stack --stack-name sms-processing-pipeline

# Clean local build artifacts
make clean
```

## Cost Optimization

- Lambda functions auto-scale based on demand
- SQS charges per message
- Database costs depend on RDS instance size
- OpenAI API costs based on usage

## Security

- All Lambda functions use least-privilege IAM roles
- Database credentials should be stored in AWS Secrets Manager
- API keys are passed as encrypted environment variables
- VPC configuration isolates database access

## Support

For issues or questions:
1. Check CloudWatch Logs for detailed error messages
2. Review database logs in `processing_logs` table
3. Monitor SQS Dead Letter Queues for failed messages
4. Validate environment configuration in `samconfig.toml`
