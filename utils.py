import re
from typing import Optional
from datetime import datetime


def clean_text(text: str) -> str:
    """
    Clean and normalize SMS text for better processing.
    
    Args:
        text: Raw SMS text
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove common SMS prefixes/suffixes
    text = re.sub(r'^(?:SMS|Alert|Notification):\s*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'\s*-\s*[A-Z]{2,}\s*BANK\s*$', '', text, flags=re.IGNORECASE)
    
    # Normalize common abbreviations
    text = re.sub(r'\bA/c\b', 'Account', text, flags=re.IGNORECASE)
    text = re.sub(r'\bTxn\b', 'Transaction', text, flags=re.IGNORECASE)
    text = re.sub(r'\bRef\b', 'Reference', text, flags=re.IGNORECASE)
    text = re.sub(r'\bAvl\b', 'Available', text, flags=re.IGNORECASE)
    text = re.sub(r'\bBal\b', 'Balance', text, flags=re.IGNORECASE)
    
    return text


def normalize_amount(amount_str: str) -> Optional[str]:
    """
    Normalize amount string to standard format.

    Args:
        amount_str: Raw amount string

    Returns:
        Normalized amount string or None if invalid
    """
    if not amount_str:
        return None

    # Remove currency symbols and extra spaces
    amount_str = re.sub(r'Rs\.?\s*', '', amount_str, flags=re.IGNORECASE)
    amount_str = re.sub(r'[₹INR\s]', '', amount_str, flags=re.IGNORECASE)

    # Remove commas
    amount_str = amount_str.replace(',', '')

    # Clean up any remaining non-numeric characters except decimal point
    amount_str = re.sub(r'[^\d\.]', '', amount_str)

    # Validate numeric format
    if not amount_str or not re.match(r'^\d+(?:\.\d{1,2})?$', amount_str):
        return None

    # Convert to float and back to ensure proper formatting
    try:
        amount = float(amount_str)
        # Format to remove unnecessary decimal places
        if amount == int(amount):
            return str(int(amount))
        else:
            return f"{amount:.2f}".rstrip('0').rstrip('.')
    except ValueError:
        return None


def normalize_date(date_str: str) -> Optional[str]:
    """
    Normalize date string to standard format.
    
    Args:
        date_str: Raw date string
        
    Returns:
        Normalized date string in DD-MM-YYYY format or None if invalid
    """
    if not date_str:
        return None
    
    date_str = date_str.strip()
    
    # Common date patterns and their formats
    date_patterns = [
        # 02May24, 15Dec2024
        (r'(\d{1,2})([A-Za-z]{3})(\d{2,4})', '%d%b%y', '%d%b%Y'),
        # 02/05/2024, 02-05-2024
        (r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})', '%d/%m/%Y', '%d-%m-%Y'),
        # 02/05/24, 02-05-24
        (r'(\d{1,2})[/-](\d{1,2})[/-](\d{2})', '%d/%m/%y', '%d-%m-%y'),
        # 2024-05-02
        (r'(\d{4})-(\d{1,2})-(\d{1,2})', '%Y-%m-%d', '%d-%m-%Y'),
    ]
    
    for pattern, input_format, output_format in date_patterns:
        match = re.match(pattern, date_str, re.IGNORECASE)
        if match:
            try:
                # Handle different date formats
                if 'May' in date_str or 'Jan' in date_str:  # Month name format
                    # Convert 2-digit year to 4-digit
                    if len(match.group(3)) == 2:
                        year = int(match.group(3))
                        if year < 50:
                            year += 2000
                        else:
                            year += 1900
                        date_str = f"{match.group(1)}{match.group(2)}{year}"
                        input_format = '%d%b%Y'
                    
                    parsed_date = datetime.strptime(date_str, input_format)
                else:
                    # Handle numeric dates
                    if input_format == '%d/%m/%y' or input_format == '%d-%m-%y':
                        day, month, year = match.groups()
                        year = int(year)
                        if year < 50:
                            year += 2000
                        else:
                            year += 1900
                        parsed_date = datetime(year, int(month), int(day))
                    else:
                        parsed_date = datetime.strptime(date_str, input_format)
                
                # Return in DD-MM-YYYY format
                return parsed_date.strftime('%d-%m-%Y')
                
            except ValueError:
                continue
    
    return None


def extract_numeric_value(text: str) -> Optional[float]:
    """
    Extract numeric value from text.
    
    Args:
        text: Text containing numeric value
        
    Returns:
        Numeric value or None if not found
    """
    if not text:
        return None
    
    # Remove non-numeric characters except decimal point
    numeric_str = re.sub(r'[^\d\.]', '', text)
    
    try:
        return float(numeric_str)
    except ValueError:
        return None


def is_valid_account_number(account_num: str) -> bool:
    """
    Validate account number format.
    
    Args:
        account_num: Account number string
        
    Returns:
        True if valid format, False otherwise
    """
    if not account_num:
        return False
    
    # Indian account numbers are typically 9-18 digits
    # May be masked with X or *
    pattern = r'^[X\*]*\d{3,}$'
    return bool(re.match(pattern, account_num, re.IGNORECASE))


def is_valid_transaction_ref(ref: str) -> bool:
    """
    Validate transaction reference format.
    
    Args:
        ref: Transaction reference string
        
    Returns:
        True if valid format, False otherwise
    """
    if not ref:
        return False
    
    # Transaction references are typically alphanumeric, 8+ characters
    return len(ref) >= 8 and ref.isalnum()


def clean_name(name: str) -> Optional[str]:
    """
    Clean and normalize person/entity names.
    
    Args:
        name: Raw name string
        
    Returns:
        Cleaned name or None if invalid
    """
    if not name:
        return None
    
    # Remove extra spaces and normalize
    name = re.sub(r'\s+', ' ', name.strip())
    
    # Remove common suffixes/prefixes
    name = re.sub(r'\s+(Ref|UPI|on|\d+).*$', '', name, flags=re.IGNORECASE)
    name = re.sub(r'^(Mr\.?|Mrs\.?|Ms\.?|Dr\.?)\s+', '', name, flags=re.IGNORECASE)
    
    # Validate name (should contain at least one letter)
    if not re.search(r'[A-Za-z]', name):
        return None
    
    # Remove special characters except spaces, dots, and hyphens
    name = re.sub(r'[^\w\s\.\-]', '', name)
    
    return name.strip() if name.strip() else None


def format_currency(amount: str, currency: str = 'INR') -> str:
    """
    Format amount with currency symbol.
    
    Args:
        amount: Amount string
        currency: Currency code
        
    Returns:
        Formatted currency string
    """
    if not amount:
        return ""
    
    currency_symbols = {
        'INR': '₹',
        'USD': '$',
        'EUR': '€',
        'GBP': '£'
    }
    
    symbol = currency_symbols.get(currency, currency)
    return f"{symbol} {amount}"


def mask_sensitive_data(data: str, mask_char: str = 'X', visible_chars: int = 4) -> str:
    """
    Mask sensitive data like account numbers, card numbers.
    
    Args:
        data: Sensitive data string
        mask_char: Character to use for masking
        visible_chars: Number of characters to keep visible at the end
        
    Returns:
        Masked string
    """
    if not data or len(data) <= visible_chars:
        return data
    
    masked_length = len(data) - visible_chars
    return mask_char * masked_length + data[-visible_chars:]


def validate_phone_number(phone: str) -> bool:
    """
    Validate Indian phone number format.
    
    Args:
        phone: Phone number string
        
    Returns:
        True if valid Indian phone number, False otherwise
    """
    if not phone:
        return False
    
    # Remove spaces, hyphens, and country code
    phone = re.sub(r'[\s\-\+]', '', phone)
    phone = re.sub(r'^91', '', phone)  # Remove India country code
    
    # Indian mobile numbers are 10 digits starting with 6-9
    pattern = r'^[6-9]\d{9}$'
    return bool(re.match(pattern, phone))


def extract_keywords(text: str, keyword_list: list) -> list:
    """
    Extract matching keywords from text.
    
    Args:
        text: Text to search in
        keyword_list: List of keywords to search for
        
    Returns:
        List of found keywords
    """
    if not text or not keyword_list:
        return []
    
    text_lower = text.lower()
    found_keywords = []
    
    for keyword in keyword_list:
        if keyword.lower() in text_lower:
            found_keywords.append(keyword)
    
    return found_keywords
