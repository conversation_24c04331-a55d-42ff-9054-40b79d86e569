package main

import (
	"context"
	"encoding/json"
	"net/http"
	"log"
	"strconv"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type ListTaskLogsResponse struct {
	Success  bool                    `json:"success"`
	Message  string                  `json:"message"`
	TaskLogs []database.TaskLogRecord `json:"taskLogs,omitempty"`
	Total    int                     `json:"total,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-list-task-logs")

	// Parse query parameters
	var req database.TaskLogListRequest
	
	// Get institution_id from path parameter
	if instIDStr := request.PathParameters["institution_id"]; instIDStr != "" {
		if instID, err := strconv.Atoi(instIDStr); err == nil {
			req.InstitutionID = &instID
		} else {
			return createErrorResponse(http.StatusBadRequest, "Invalid institution_id in path")
		}
	} else {
		return createErrorResponse(http.StatusBadRequest, "institution_id is required in path")
	}

	// Parse arrays from query parameters
	if statusStr := request.QueryStringParameters["status"]; statusStr != "" {
		req.Status = strings.Split(statusStr, ",")
	}
	if identifierTypeStr := request.QueryStringParameters["identifier_type"]; identifierTypeStr != "" {
		req.IdentifierType = strings.Split(identifierTypeStr, ",")
	}
	if taskTypeStr := request.QueryStringParameters["task_type"]; taskTypeStr != "" {
		req.TaskType = strings.Split(taskTypeStr, ",")
	}

	// Parse sorting
	req.SortBy = request.QueryStringParameters["sort_by"]
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	req.SortOrder = request.QueryStringParameters["sort_order"]
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// Parse pagination
	if limitStr := request.QueryStringParameters["limit"]; limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			req.Limit = limit
		} else {
			req.Limit = 50
		}
	} else {
		req.Limit = 50
	}

	if offsetStr := request.QueryStringParameters["offset"]; offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			req.Offset = offset
		}
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	taskLogs, total, err := db.ListTaskLogsWithFilters(req)
	if err != nil {
		log.Printf("Failed to list task logs: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to list task logs")
	}

	// Create audit log entry for task logs listing
	auditLog := &database.AuditLog{
		EventType:     "task_logs_listed",
		InstitutionID: req.InstitutionID,
		UserID:        "system", // Since no user ID is provided in the request
		Metadata: map[string]interface{}{
			"task_logs_count":  len(taskLogs),
			"total_count":      total,
			"limit":            req.Limit,
			"offset":           req.Offset,
			"sort_by":          req.SortBy,
			"sort_order":       req.SortOrder,
			"filters_applied":  len(req.Status) + len(req.IdentifierType) + len(req.TaskType),
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	response := ListTaskLogsResponse{
		Success:  true,
		Message:  "Task logs fetched successfully",
		TaskLogs: taskLogs,
		Total:    total,
	}
	responseJSON, _ := json.Marshal(response)
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	responseJSON, _ := json.Marshal(errorResponse)
	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
} 