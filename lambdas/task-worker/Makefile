# Makefile for the task worker <PERSON><PERSON> function
.PHONY: build clean

build:
	@echo "Building TaskWorkerFunction..."
	cd /Users/<USER>/Downloads/secure_double_blind_ec && \
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 \
	go build -ldflags="-s -w" -o "$(ARTIFACTS_DIR)/bootstrap" lambdas/task-worker/main.go
	@echo "TaskWorkerFunction built successfully!"

clean:
	@echo "Cleaning build artifacts for TaskWorkerFunction..."
	rm -f bootstrap
	@echo "Clean completed!"

# Targets for SAM build
build-TaskWorkerFunction: build 