# Universal Makefile for all Lambda Functions
# Usage: make -f /path/to/universal.mk FUNCTION_NAME=YourFunctionName LAMBDA_PATH=path/to/lambda

.PHONY: build clean

# Go build parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

# Root directory of the project
ROOT_DIR=/Users/<USER>/Downloads/secure_double_blind_ec

# Default values
FUNCTION_NAME?=UnknownFunction
LAMBDA_PATH?=unknown

build:
	@echo "Building $(FUNCTION_NAME) function..."
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
	go build -ldflags="-s -w" -o $(ARTIFACTS_DIR)/bootstrap $(LAMBDA_PATH)/main.go
	@echo "$(FUNCTION_NAME) built successfully!"

clean:
	@echo "Cleaning build artifacts for $(FUNCTION_NAME)..."
	rm -f bootstrap
	@echo "Clean completed!"
