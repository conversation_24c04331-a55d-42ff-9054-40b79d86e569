package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/google/uuid"
	"secure_double_blind_ec/pkg/database"
)

type TaskMessage struct {
	TaskID       string `json:"task_id"`
	CompanyID    string `json:"company_id"`
	Identifier   string `json:"identifier"`
	BlindedValue string `json:"blinded_value"`
}

type CrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"` // Hex-encoded blinded identifier
	RequestingCompany string `json:"requesting_company"` // Company making the request
}

type CrossQueryResponse struct {
	Success       bool              `json:"success"`
	Message       string            `json:"message"`
	BlindedValues map[string]string `json:"blinded_values,omitempty"` // Map of company_id -> double_encrypted_value (hex)
}

type GetDataRequest struct {
	BlindedIdentifiers []string `json:"blinded_identifiers"` // Hex-encoded blinded identifiers
}

type GetDataResponse struct {
	Success   bool                    `json:"success"`
	Message   string                  `json:"message"`
	FraudData []*database.FraudData   `json:"fraud_data,omitempty"`
}

func handler(ctx context.Context, sqsEvent events.SQSEvent) error {
	for _, record := range sqsEvent.Records {
		log.Printf("Processing SQS message ID: %s", record.MessageId)
		
		// Parse task message
		var task TaskMessage
		if err := json.Unmarshal([]byte(record.Body), &task); err != nil {
			log.Printf("Error unmarshaling task message: %v", err)
			continue
		}
		
		log.Printf("Processing task ID: %s for company: %s", task.TaskID, task.CompanyID)
		
		// Process the task
		if err := processTask(task); err != nil {
			log.Printf("Error processing task: %v", err)
			
			// Update task status to failed
			taskID, _ := uuid.Parse(task.TaskID)
			db, dbErr := database.New()
			if dbErr == nil {
				defer db.Close()
				
				taskLog, _ := db.GetTaskLogByTaskID(taskID)
				if taskLog != nil {
					taskLog.Status = "failed"
					errorMsg := err.Error()
					taskLog.Error = &errorMsg
					now := time.Now()
					taskLog.UpdatedAt = now
					taskLog.EndTime = &now
					
					// Add error step
					step := map[string]interface{}{
						"step":        "task_failed",
						"description": fmt.Sprintf("Task failed: %v", err),
						"timestamp":   now.Format(time.RFC3339),
						"status":      "failed",
					}
					
					db.AddStepToTaskLog(taskID, step)
					db.UpdateTaskLog(taskLog)
				}
			}
			
			continue
		}
		
		log.Printf("Task ID: %s completed successfully", task.TaskID)
	}
	
	return nil
}

func processTask(task TaskMessage) error {
	// Parse task ID
	taskID, err := uuid.Parse(task.TaskID)
	if err != nil {
		return fmt.Errorf("invalid task ID format: %v", err)
	}
	
	// Connect to database
	db, err := database.New()
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}
	defer db.Close()
	
	// Step 1: Update task status to show we're starting
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("failed to get task log: %v", err)
	}
	
	taskLog.Status = "processing"
	now := time.Now()
	taskLog.UpdatedAt = now
	
	// Add step 1 to task log if not already present
	step1Found := false
	for _, step := range taskLog.Steps {
		if step["step"] == "secure_query_creation" {
			step1Found = true
			break
		}
	}
	
	if !step1Found {
		step1 := map[string]interface{}{
			"step":        "secure_query_creation",
			"description": "Query securely created and signed by institution",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "completed",
		}
		
		err = db.AddStepToTaskLog(taskID, step1)
		if err != nil {
			return fmt.Errorf("failed to add step 1 to task log: %v", err)
		}
	}
	
	err = db.UpdateTaskLog(taskLog)
	if err != nil {
		return fmt.Errorf("failed to update task log: %v", err)
	}
	
	// Step 2: Encrypted Partner Communication
	err = executeStep2EncryptedPartnerCommunication(db, taskID, task)
	if err != nil {
		updateTaskLogWithError(db, taskID, "encrypted_partner_communication", err.Error())
		return fmt.Errorf("step 2 failed: %v", err)
	}
	
	// Step 3: Encrypted Consortium Lookup
	blindedValues, err := executeStep3EncryptedConsortiumLookup(db, taskID, task)
	if err != nil {
		updateTaskLogWithError(db, taskID, "encrypted_consortium_lookup", err.Error())
		return fmt.Errorf("step 3 failed: %v", err)
	}
	
	// Step 4: Report Generation
	err = executeStep4ReportGeneration(db, taskID, task, blindedValues)
	if err != nil {
		updateTaskLogWithError(db, taskID, "report_generation", err.Error())
		return fmt.Errorf("step 4 failed: %v", err)
	}
	
	return nil
}

// updateTaskLogWithError updates the task log with an error for the specific step
func updateTaskLogWithError(db *database.DB, taskID uuid.UUID, stepName, errorMsg string) {
	// Add error step
	now := time.Now()
	errorStep := map[string]interface{}{
		"step":        stepName,
		"description": fmt.Sprintf("Error in %s: %s", stepName, errorMsg),
		"timestamp":   now.Format(time.RFC3339),
		"status":      "failed",
	}
	
	// Ignore errors here since we're already in an error path
	db.AddStepToTaskLog(taskID, errorStep)
	
	// Update task log status
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err == nil && taskLog != nil {
		taskLog.Status = "failed"
		errMsg := errorMsg
		taskLog.Error = &errMsg
		taskLog.UpdatedAt = now
		taskLog.EndTime = &now
		
		db.UpdateTaskLog(taskLog)
	}
}

func executeStep2EncryptedPartnerCommunication(db *database.DB, taskID uuid.UUID, task TaskMessage) error {
	log.Printf("Executing Step 2: Encrypted Partner Communication for task %s", taskID)
	
	// Update task status to processing
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("failed to get task log: %v", err)
	}
	
	// Add step to task log
	now := time.Now()
	step := map[string]interface{}{
		"step":        "encrypted_partner_communication",
		"description": "Query encrypted and sent to partner institutions",
		"timestamp":   now.Format(time.RFC3339),
		"status":      "completed",
	}
	
	err = db.AddStepToTaskLog(taskID, step)
	if err != nil {
		return fmt.Errorf("failed to add step to task log: %v", err)
	}
	
	// Update task status
	taskLog.UpdatedAt = now
	err = db.UpdateTaskLog(taskLog)
	if err != nil {
		return fmt.Errorf("failed to update task log: %v", err)
	}
	
	// Simulate a short delay for the encryption process
	time.Sleep(500 * time.Millisecond)
	
	log.Printf("Step 2 completed for task %s", taskID)
	return nil
}

func executeStep3EncryptedConsortiumLookup(db *database.DB, taskID uuid.UUID, task TaskMessage) (map[string]string, error) {
	log.Printf("Executing Step 3: Encrypted Consortium Lookup for task %s", taskID)
	
	// Add step to task log with "in_progress" status
	now := time.Now()
	stepStart := map[string]interface{}{
		"step":        "encrypted_consortium_lookup",
		"description": "Consortium-wide lookup and validation in progress",
		"timestamp":   now.Format(time.RFC3339),
		"status":      "in_progress",
	}
	
	err := db.AddStepToTaskLog(taskID, stepStart)
	if err != nil {
		return nil, fmt.Errorf("failed to add step start to task log: %v", err)
	}
	
	// Create cross-query request to central server
	crossQueryReq := CrossQueryRequest{
		BlindedIdentifier: task.BlindedValue,
		RequestingCompany: task.CompanyID,
	}
	
	// Call the central cross-query API
	baseAPIURL := os.Getenv("BASE_API_URL")
	if baseAPIURL == "" {
		// Fallback to older env var for backward compatibility
		baseAPIURL = os.Getenv("CENTRAL_API_URL")
		if baseAPIURL == "" {
			return nil, fmt.Errorf("BASE_API_URL environment variable not set")
		}
	}
	
	// Ensure the URL ends with a slash
	if !strings.HasSuffix(baseAPIURL, "/") {
		baseAPIURL += "/"
	}
	
	url := fmt.Sprintf("%scentral/cross-query", baseAPIURL)
	
	requestBody, err := json.Marshal(crossQueryReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal cross-query request: %v", err)
	}
	
	log.Printf("Calling central cross-query API: %s", url)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call central cross-query API: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		// Read response body for more details
		var errorResponse []byte = make([]byte, 1024)
		n, _ := resp.Body.Read(errorResponse)
		return nil, fmt.Errorf("central server returned status: %d, body: %s", resp.StatusCode, errorResponse[:n])
	}
	
	var crossQueryResp CrossQueryResponse
	if err := json.NewDecoder(resp.Body).Decode(&crossQueryResp); err != nil {
		return nil, fmt.Errorf("failed to decode cross-query response: %v", err)
	}
	
	if !crossQueryResp.Success {
		return nil, fmt.Errorf("central cross-query API returned failure: %s", crossQueryResp.Message)
	}
	
	// Update step to "completed"
	now = time.Now()
	stepComplete := map[string]interface{}{
		"step":        "encrypted_consortium_lookup",
		"description": "Consortium-wide lookup and validation completed",
		"timestamp":   now.Format(time.RFC3339),
		"status":      "completed",
	}
	
	err = db.AddStepToTaskLog(taskID, stepComplete)
	if err != nil {
		return nil, fmt.Errorf("failed to add step completion to task log: %v", err)
	}
	
	log.Printf("Step 3 completed for task %s", taskID)
	return crossQueryResp.BlindedValues, nil
}

func executeStep4ReportGeneration(db *database.DB, taskID uuid.UUID, task TaskMessage, blindedValues map[string]string) error {
	log.Printf("Executing Step 4: Report Generation for task %s", taskID)
	
	// Add step to task log with "in_progress" status
	now := time.Now()
	stepStart := map[string]interface{}{
		"step":        "report_generation",
		"description": "Final report generation in progress",
		"timestamp":   now.Format(time.RFC3339),
		"status":      "in_progress",
	}
	
	err := db.AddStepToTaskLog(taskID, stepStart)
	if err != nil {
		return fmt.Errorf("failed to add step start to task log: %v", err)
	}
	
	// Prepare list of blinded identifiers to query
	var blindedIdentifiers []string
	for _, value := range blindedValues {
		blindedIdentifiers = append(blindedIdentifiers, value)
	}
	
	// Call the central get-data API
	getDataReq := GetDataRequest{
		BlindedIdentifiers: blindedIdentifiers,
	}
	
	baseAPIURL := os.Getenv("BASE_API_URL")
	if baseAPIURL == "" {
		// Fallback to older env var for backward compatibility
		baseAPIURL = os.Getenv("CENTRAL_API_URL")
		if baseAPIURL == "" {
			return fmt.Errorf("BASE_API_URL environment variable not set")
		}
	}
	
	// Ensure the URL ends with a slash
	if !strings.HasSuffix(baseAPIURL, "/") {
		baseAPIURL += "/"
	}
	
	url := fmt.Sprintf("%scentral/get-data", baseAPIURL)
	
	requestBody, err := json.Marshal(getDataReq)
	if err != nil {
		return fmt.Errorf("failed to marshal get-data request: %v", err)
	}
	
	log.Printf("Calling central get-data API: %s", url)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to call central get-data API: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		// Read response body for more details
		var errorResponse []byte = make([]byte, 1024)
		n, _ := resp.Body.Read(errorResponse)
		return fmt.Errorf("central server returned status: %d, body: %s", resp.StatusCode, errorResponse[:n])
	}
	
	var getDataResp GetDataResponse
	if err := json.NewDecoder(resp.Body).Decode(&getDataResp); err != nil {
		return fmt.Errorf("failed to decode get-data response: %v", err)
	}
	
	if !getDataResp.Success {
		return fmt.Errorf("central get-data API returned failure: %s", getDataResp.Message)
	}
	
	// Get task log
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		return fmt.Errorf("failed to get task log: %v", err)
	}
	
	// Update task log
	taskLog.Status = "completed"
	taskLog.Response = map[string]interface{}{
		"success":    true,
		"message":    "Fraud data retrieved successfully",
		"fraud_data": getDataResp.FraudData,
	}
	now = time.Now()
	taskLog.UpdatedAt = now
	taskLog.QueryCompletedAt = &now
	taskLog.EndTime = &now
	
	// Add step to "completed"
	stepComplete := map[string]interface{}{
		"step":        "report_generation",
		"description": "Final report generated and delivered",
		"timestamp":   now.Format(time.RFC3339),
		"status":      "completed",
	}
	
	err = db.AddStepToTaskLog(taskID, stepComplete)
	if err != nil {
		return fmt.Errorf("failed to add step completion to task log: %v", err)
	}
	
	// Update task log
	err = db.UpdateTaskLog(taskLog)
	if err != nil {
		return fmt.Errorf("failed to update task log: %v", err)
	}
	
	log.Printf("Step 4 completed for task %s", taskID)
	return nil
}

func main() {
	lambda.Start(handler)
} 