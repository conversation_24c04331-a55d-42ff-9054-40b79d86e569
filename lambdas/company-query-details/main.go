package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/google/uuid"
	"secure_double_blind_ec/pkg/database"
)

type QueryDetailsRequest struct {
	TaskID  string `json:"task_id,omitempty"`
	QueryID string `json:"query_id,omitempty"`
}

type QueryDetailsResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    *QueryDetailsData      `json:"data,omitempty"`
	Error   *string                `json:"error,omitempty"`
}

type QueryDetailsData struct {
	TaskID                string                   `json:"task_id"`
	QueryID               string                   `json:"query_id"`
	InstitutionID         int                      `json:"institution_id"`
	InstitutionName       string                   `json:"institution_name"`
	InstitutionType       string                   `json:"institution_type"`
	Status                string                   `json:"status"`
	ResponseTimeMs        int                      `json:"response_time_ms"`
	CreatedAt             string                   `json:"created_at"`
	UpdatedAt             string                   `json:"updated_at"`
	EncryptionTable       []EncryptionTableRow     `json:"encryption_table"`
	OriginalIdentifiers   []map[string]interface{} `json:"original_identifiers"`
	FraudDataResults      []map[string]interface{} `json:"fraud_data_results"`
	Summary               map[string]interface{}   `json:"summary"`
}

type EncryptionTableRow struct {
	RowID                 int    `json:"row_id"`
	QueryID               string `json:"query_id"`               // Task ID from task log
	OriginalIdentifier    string `json:"original_identifier"`
	IdentifierType        string `json:"identifier_type"`
	EncryptedQuery        string `json:"encrypted_query"`        // A_Bq
	Intermediate          string `json:"intermediate"`           // A_Bq_Bn
	EncryptedByPartner    string `json:"encrypted_by_partner"`   // A_Bn
	Consortium            string `json:"consortium"`             // A_Bn (final)
	CompanyID             string `json:"company_id,omitempty"`
	PartnerCompanyID      string `json:"partner_company_id,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-query-details")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	// Parse request body
	var req QueryDetailsRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	// Validate that either task_id or query_id is provided
	if req.TaskID == "" && req.QueryID == "" {
		log.Println("Missing required field: either task_id or query_id")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: either task_id or query_id")
	}
	
	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()
	
	// Get query response details
	var details *database.QueryResponseDetails
	if req.TaskID != "" {
		taskUUID, err := uuid.Parse(req.TaskID)
		if err != nil {
			log.Printf("Invalid task_id format: %v", err)
			return createErrorResponse(http.StatusBadRequest, "Invalid task_id format")
		}
		details, err = db.GetQueryResponseDetailsByTaskID(taskUUID)
	} else {
		details, err = db.GetQueryResponseDetailsByQueryID(req.QueryID)
	}
	
	if err != nil {
		log.Printf("Failed to get query response details: %v", err)
		return createErrorResponse(http.StatusNotFound, "Query response details not found")
	}
	
	// Verify institution access
	if details.InstitutionID != institutionID {
		log.Printf("Institution ID mismatch: requested %d, but details belong to %d", institutionID, details.InstitutionID)
		return createErrorResponse(http.StatusForbidden, "Access denied to this query")
	}
	
	// Get institution information
	institutionName, institutionType := getInstitutionInfo(institutionID)
	
	// Build encryption table
	encryptionTable := buildEncryptionTable(details)
	
	// Extract original identifiers
	originalIdentifiers := extractOriginalIdentifiers(details.OriginalIdentifiers)
	
	// Extract fraud data results
	fraudDataResults := extractFraudDataResults(details.FraudDataResults)
	
	// Create response data
	responseData := &QueryDetailsData{
		TaskID:                details.TaskID.String(),
		QueryID:               details.QueryID,
		InstitutionID:         details.InstitutionID,
		InstitutionName:       institutionName,
		InstitutionType:       institutionType,
		Status:                details.Status,
		ResponseTimeMs:        details.ResponseTimeMs,
		CreatedAt:             details.CreatedAt.Format(time.RFC3339),
		UpdatedAt:             details.UpdatedAt.Format(time.RFC3339),
		EncryptionTable:       encryptionTable,
		OriginalIdentifiers:   originalIdentifiers,
		FraudDataResults:      fraudDataResults,
		Summary:               details.ResponseSummary,
	}
	
	// Create success response
	response := QueryDetailsResponse{
		Success: true,
		Message: "Query details retrieved successfully",
		Data:    responseData,
	}
	
	responseJSON, _ := json.Marshal(response)
	
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

// buildEncryptionTable creates the encryption table showing the progression of encrypted values
func buildEncryptionTable(details *database.QueryResponseDetails) []EncryptionTableRow {
	var table []EncryptionTableRow
	rowID := 1
	
	// Extract blinded identifiers
	blindedIdentifiers := extractBlindedIdentifiers(details.BlindedIdentifiers)
	
	// Extract double encrypted values (from cross-query)
	doubleEncryptedValues := extractDoubleEncryptedValues(details.DoubleEncryptedValues)
	
	// Extract single decrypted values (from other companies)
	singleDecryptedValues := extractSingleDecryptedValues(details.SingleDecryptedValues)
	
	// Create company ID for the requesting institution
	requestingCompanyID := fmt.Sprintf("company_%d", details.InstitutionID)
	
	// Process each blinded identifier
	for i, blindedId := range blindedIdentifiers {
		identifier := blindedId["id"].(string)
		identifierType := blindedId["type"].(string)
		
		// Get original identifier (we'll need to extract this from the original identifiers)
		originalIdentifier := getOriginalIdentifier(details.OriginalIdentifiers, i)
		
		// Create base row
		row := EncryptionTableRow{
			RowID:              rowID,
			QueryID:            details.TaskID.String(), // Use task ID as query ID
			OriginalIdentifier: originalIdentifier,
			IdentifierType:     identifierType,
			EncryptedQuery:     identifier, // A_Bq - blinded identifier sent to central
			CompanyID:          requestingCompanyID,
		}
		
		// Add intermediate and partner encrypted values for each company
		for companyID, doubleEncryptedValue := range doubleEncryptedValues {
			if companyID == requestingCompanyID {
				continue // Skip our own company
			}
			
			// Create a new row for each partner company
			partnerRow := row
			partnerRow.RowID = rowID
			partnerRow.QueryID = details.TaskID.String() // Use task ID as query ID
			partnerRow.Intermediate = doubleEncryptedValue // A_Bq_Bn - double encrypted
			partnerRow.EncryptedByPartner = getSingleDecryptedValue(singleDecryptedValues, companyID) // A_Bn - single decrypted
			partnerRow.Consortium = partnerRow.EncryptedByPartner // A_Bn - final value
			partnerRow.PartnerCompanyID = companyID
			
			table = append(table, partnerRow)
			rowID++
		}
		
		// If no partner companies, add the base row
		if len(doubleEncryptedValues) == 1 { // Only our own company
			row.Consortium = row.EncryptedQuery
			table = append(table, row)
			rowID++
		}
	}
	
	return table
}

// extractBlindedIdentifiers extracts blinded identifiers from the JSON data
func extractBlindedIdentifiers(data map[string]interface{}) []map[string]interface{} {
	var result []map[string]interface{}
	
	for _, value := range data {
		if identifierData, ok := value.(map[string]interface{}); ok {
			result = append(result, identifierData)
		}
	}
	
	return result
}

// extractDoubleEncryptedValues extracts double encrypted values from the JSON data
func extractDoubleEncryptedValues(data map[string]interface{}) map[string]string {
	result := make(map[string]string)
	
	for companyID, value := range data {
		if encryptedValue, ok := value.(string); ok {
			result[companyID] = encryptedValue
		}
	}
	
	return result
}

// extractSingleDecryptedValues extracts single decrypted values from the JSON data
func extractSingleDecryptedValues(data map[string]interface{}) map[string]string {
	result := make(map[string]string)
	
	for companyID, value := range data {
		if decryptedValue, ok := value.(string); ok {
			result[companyID] = decryptedValue
		}
	}
	
	return result
}

// getOriginalIdentifier extracts the original identifier from the original identifiers data
func getOriginalIdentifier(data map[string]interface{}, index int) string {
	if identifiers, ok := data["identifiers"].([]interface{}); ok && index < len(identifiers) {
		if identifierData, ok := identifiers[index].(map[string]interface{}); ok {
			if identifier, ok := identifierData["identifier"].(string); ok {
				return identifier
			}
		}
	}
	return fmt.Sprintf("identifier_%d", index)
}

// getSingleDecryptedValue gets the single decrypted value for a specific company
func getSingleDecryptedValue(data map[string]string, companyID string) string {
	if value, exists := data[companyID]; exists {
		return value
	}
	return ""
}

// extractOriginalIdentifiers extracts original identifiers from the JSON data
func extractOriginalIdentifiers(data map[string]interface{}) []map[string]interface{} {
	var result []map[string]interface{}
	
	if identifiers, ok := data["identifiers"].([]interface{}); ok {
		for _, identifier := range identifiers {
			if identifierData, ok := identifier.(map[string]interface{}); ok {
				result = append(result, identifierData)
			}
		}
	}
	
	return result
}

// extractFraudDataResults extracts fraud data results from the JSON data
func extractFraudDataResults(data map[string]interface{}) []map[string]interface{} {
	var result []map[string]interface{}
	
	if results, ok := data["results"].([]interface{}); ok {
		for _, resultItem := range results {
			if resultData, ok := resultItem.(map[string]interface{}); ok {
				result = append(result, resultData)
			}
		}
	}
	
	return result
}

// Helper function to fetch institution information from database
func getInstitutionInfo(institutionID int) (string, string) {
	institutionName := "Unknown Institution"
	var institutionType string
	
	db, err := database.New()
	if err == nil {
		defer db.Close()
		institution, err := db.GetInstitutionByID(institutionID)
		if err == nil {
			if institution.InstitutionName != "" {
				institutionName = institution.InstitutionName
			}
			if institution.InstitutionType != nil {
				institutionType = *institution.InstitutionType
			}
		}
	}
	
	return institutionName, institutionType
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := QueryDetailsResponse{
		Success: false,
		Message: message,
		Error:   &message,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
} 