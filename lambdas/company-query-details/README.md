# Company Query Details API

This Lambda function provides detailed information about query responses, including the encryption table that shows the progression of encrypted values through the secure double-blind encryption process.

## API Endpoint

```
POST /institution/{institution_id}/query-details
```

## Request Format

The request body should contain either a `task_id` or `query_id`:

```json
{
  "task_id": "uuid-string",
  "query_id": "QRY-YYYYMMDD-XXX"
}
```

## Response Format

```json
{
  "success": true,
  "message": "Query details retrieved successfully",
  "data": {
    "task_id": "uuid-string",
    "query_id": "QRY-YYYYMMDD-XXX",
    "institution_id": 1,
    "institution_name": "Test Institution",
    "institution_type": "university",
    "status": "completed",
    "response_time_ms": 1500,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:05Z",
    "encryption_table": [
      {
        "row_id": 1,
        "query_id": "550e8400-e29b-41d4-a716-************",
        "original_identifier": "<EMAIL>",
        "identifier_type": "email",
        "encrypted_query": "a1_Bq",
        "intermediate": "a1_Bq_B1",
        "encrypted_by_partner": "a1_B1",
        "consortium": "a1_B1",
        "company_id": "company_1",
        "partner_company_id": "company_2"
      }
    ],
    "original_identifiers": [
      {
        "identifier": "<EMAIL>",
        "type": "email"
      }
    ],
    "fraud_data_results": [
      {
        "id": 123,
        "identifier": "<EMAIL>",
        "identifier_type": "email",
        "status": "Confirmed",
        "fraud_type": "identity_theft",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "summary": {
      "total_identifiers": 1,
      "total_matches": 1,
      "query_date": "2024-01-15 10:30:00",
      "status": "completed"
    }
  }
}
```

## Encryption Table Structure

The encryption table shows the progression of encrypted values through the secure double-blind encryption process:

| Column | Description | Example |
|--------|-------------|---------|
| `query_id` | Task ID from task log table | `550e8400-e29b-41d4-a716-************` |
| `encrypted_query` | Blinded identifier sent to central server (A_Bq) | `a1_Bq` |
| `intermediate` | Double encrypted value (A_Bq_Bn) | `a1_Bq_B1` |
| `encrypted_by_partner` | Single decrypted value from partner (A_Bn) | `a1_B1` |
| `consortium` | Final value used in consortium lookup (A_Bn) | `a1_B1` |
| `company_id` | ID of the requesting company | `company_1` |
| `partner_company_id` | ID of the partner company (if applicable) | `company_2` |

## Encryption Process Flow

1. **Encrypted Query (A_Bq)**: The original identifier is blinded using the requesting company's blinding factor
2. **Intermediate (A_Bq_Bn)**: The blinded identifier is further encrypted by partner companies
3. **Encrypted by Partner (A_Bn)**: The double-encrypted value is decrypted once by the requesting company
4. **Consortium (A_Bn)**: The final value used for consortium-wide lookup

## Error Responses

```json
{
  "success": false,
  "message": "Error description",
  "error": "Error description"
}
```

Common error codes:
- `400`: Bad Request (missing parameters, invalid format)
- `403`: Forbidden (institution access denied)
- `404`: Not Found (query details not found)
- `500`: Internal Server Error (database connection issues)

## Usage Examples

### Using Task ID
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"task_id": "550e8400-e29b-41d4-a716-************"}' \
  https://api.example.com/institution/1/query-details
```

### Using Query ID
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query_id": "QRY-20240115-001"}' \
  https://api.example.com/institution/1/query-details
```

## Security

- Only the institution that created the query can access its details
- All data is encrypted in transit and at rest
- Access is validated against the institution ID in the URL path

## Dependencies

- AWS Lambda Go runtime
- PostgreSQL database connection
- UUID parsing for task IDs
- JSON marshaling/unmarshaling

## Build and Deploy

```bash
# Build the function
make build

# Package for deployment
make package

# Deploy to AWS
make deploy
``` 