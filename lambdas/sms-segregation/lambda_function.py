"""
Lambda 1: SMS Segregation Function
Processes messages from SMS Input Queue, segregates into financial/non-financial,
stores in database, and sends batches to SMS Batch Queue.
"""
import json
import logging
import os
from typing import Dict, List, Any

# Import from shared layer
from database import db_manager
from sms_classifier import sms_classifier
from utils import (
    setup_logging, read_file_from_s3, parse_sms_file, 
    send_sqs_message, batch_list, get_environment_variable,
    validate_message_format, create_error_response, create_success_response
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

def lambda_handler(event, context):
    """
    Main Lambda handler for SMS segregation
    
    Expected SQS message format:
    {
        "s3_path": "s3://bucket/path/to/sms/file.csv",
        "customer_id": "customer_123"
    }
    """
    try:
        logger.info(f"Processing {len(event.get('Records', []))} SQS messages")
        
        # Process each SQS record
        for record in event.get('Records', []):
            try:
                process_sqs_record(record)
            except Exception as e:
                logger.error(f"Failed to process SQS record: {str(e)}")
                # Continue processing other records
                continue
        
        return create_success_response(message="SMS segregation completed successfully")
    
    except Exception as e:
        logger.error(f"Lambda execution failed: {str(e)}")
        return create_error_response(f"Lambda execution failed: {str(e)}")

def process_sqs_record(record: Dict[str, Any]):
    """Process a single SQS record"""
    try:
        # Parse SQS message
        message_body = json.loads(record['body'])
        logger.info(f"Processing message: {message_body}")
        
        # Validate message format
        if not validate_message_format(message_body):
            raise ValueError("Invalid message format")
        
        customer_id = message_body['customer_id']
        s3_path = message_body.get('s3_path')
        
        if not s3_path:
            raise ValueError("s3_path is required")
        
        # Log processing start
        db_manager.log_processing_event(
            customer_id=customer_id,
            log_level='INFO',
            message=f"Started SMS segregation for customer {customer_id}",
            metadata={'s3_path': s3_path}
        )
        
        # Read and parse SMS file from S3
        logger.info(f"Reading SMS file from S3: {s3_path}")
        file_content = read_file_from_s3(s3_path)
        
        # Determine file format from extension
        file_format = 'csv'  # default
        if s3_path.lower().endswith('.json'):
            file_format = 'json'
        elif s3_path.lower().endswith('.txt'):
            file_format = 'txt'
        
        # Parse messages from file
        messages = parse_sms_file(file_content, file_format)
        logger.info(f"Parsed {len(messages)} messages from file")
        
        if not messages:
            logger.warning("No messages found in file")
            return
        
        # Classify and store messages
        financial_messages = []
        non_financial_messages = []
        stored_message_ids = []
        
        for message_data in messages:
            message_text = message_data['text']
            
            # Classify message
            classification = sms_classifier.classify_message(message_text)
            message_type = classification['message_type']
            
            # Store message in database
            message_id = db_manager.insert_sms_message(
                customer_id=customer_id,
                message_text=message_text,
                message_type=message_type,
                s3_path=s3_path
            )
            
            stored_message_ids.append(message_id)
            
            if message_type == 'financial':
                financial_messages.append(message_id)
            else:
                non_financial_messages.append(message_id)
            
            logger.debug(f"Stored message {message_id} as {message_type}")
        
        # Initialize customer processing status
        db_manager.initialize_customer_status(
            customer_id=customer_id,
            total_messages=len(messages),
            financial_count=len(financial_messages),
            non_financial_count=len(non_financial_messages)
        )
        
        logger.info(f"Classified {len(financial_messages)} financial and {len(non_financial_messages)} non-financial messages")
        
        # Create batches and send to SMS Batch Queue
        batch_size = int(get_environment_variable('BATCH_SIZE', 50))
        all_message_ids = stored_message_ids
        
        if all_message_ids:
            send_message_batches(customer_id, all_message_ids, batch_size)
        
        # Log completion
        db_manager.log_processing_event(
            customer_id=customer_id,
            log_level='INFO',
            message=f"Completed SMS segregation for customer {customer_id}",
            metadata={
                'total_messages': len(messages),
                'financial_messages': len(financial_messages),
                'non_financial_messages': len(non_financial_messages),
                'batches_created': len(batch_list(all_message_ids, batch_size))
            }
        )
        
        logger.info(f"Successfully processed SMS segregation for customer {customer_id}")
    
    except Exception as e:
        logger.error(f"Failed to process SQS record: {str(e)}")
        
        # Log error if we have customer_id
        try:
            message_body = json.loads(record['body'])
            customer_id = message_body.get('customer_id')
            if customer_id:
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    log_level='ERROR',
                    message=f"SMS segregation failed: {str(e)}",
                    metadata={'error_type': type(e).__name__}
                )
        except:
            pass
        
        raise

def send_message_batches(customer_id: str, message_ids: List[str], batch_size: int):
    """Create batches and send to SMS Batch Queue"""
    try:
        sms_batch_queue_url = get_environment_variable('SMS_BATCH_QUEUE_URL', required=True)
        
        # Split message IDs into batches
        batches = batch_list(message_ids, batch_size)
        
        logger.info(f"Creating {len(batches)} batches for customer {customer_id}")
        
        for i, batch_message_ids in enumerate(batches):
            # Create batch processing record
            batch_id = db_manager.create_batch_processing(customer_id, batch_message_ids)
            
            # Prepare batch message for SQS
            batch_message = {
                'customer_id': customer_id,
                'message_ids': batch_message_ids,
                'batch_id': batch_id,
                'batch_number': i + 1,
                'total_batches': len(batches)
            }
            
            # Send to SMS Batch Queue
            message_id = send_sqs_message(sms_batch_queue_url, batch_message)
            
            logger.info(f"Sent batch {i + 1}/{len(batches)} to SMS Batch Queue: {message_id}")
            
            # Log batch creation
            db_manager.log_processing_event(
                customer_id=customer_id,
                batch_id=batch_id,
                log_level='INFO',
                message=f"Created batch {i + 1}/{len(batches)} with {len(batch_message_ids)} messages",
                metadata={
                    'batch_size': len(batch_message_ids),
                    'sqs_message_id': message_id
                }
            )
    
    except Exception as e:
        logger.error(f"Failed to send message batches: {str(e)}")
        raise
