package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type EncryptRequest struct {
	EncryptedValue string `json:"encrypted_value"` // Hex-encoded already encrypted value
}

type EncryptResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	EncryptedValue string `json:"encrypted_value,omitempty"` // Hex-encoded double encrypted value
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-encrypt")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	// Create company ID from institution ID
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req EncryptRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.EncryptedValue == "" {
		log.Println("Missing required field: encrypted_value")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: encrypted_value")
	}

	// Initialize blinding service for this company
	log.Printf("Initializing blinding service for company: %s", companyID)
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initialize blinding service")
	}

	// Decode hex string to bytes
	encryptedBytes, err := hex.DecodeString(req.EncryptedValue)
	if err != nil {
		log.Printf("Failed to decode hex string: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid encrypted_value format")
	}

	// Convert bytes to group element
	encryptedElement := blindingService.GetGroup().NewElement()
	if err := encryptedElement.UnmarshalBinary(encryptedBytes); err != nil {
		log.Printf("Failed to unmarshal group element: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid encrypted_value data")
	}

	// Apply company's encryption to the already encrypted value
	log.Println("Applying company encryption to already encrypted value")
	doubleEncrypted, err := blindingService.EncryptAlreadyEncryptedValue(encryptedElement)
	if err != nil {
		log.Printf("Failed to encrypt value: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to encrypt value")
	}

	// Convert encrypted element to hex string
	doubleEncryptedBytes, err := doubleEncrypted.MarshalBinary()
	if err != nil {
		log.Printf("Failed to serialize encrypted element: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize encrypted element")
	}
	doubleEncryptedHex := hex.EncodeToString(doubleEncryptedBytes)

	// Prepare response
	response := EncryptResponse{
		Success:        true,
		Message:        "Value encrypted successfully",
		EncryptedValue: doubleEncryptedHex,
	}

	// Create audit log entry for encryption operation
	db, err := database.New()
	if err == nil {
		defer db.Close()
		
		auditLog := &database.AuditLog{
			EventType:     "data_encrypted",
			InstitutionID: &institutionID,
			UserID:        "system", // Since no user ID is provided in the request
			Metadata: map[string]interface{}{
				"operation":        "double_encryption",
				"input_length":     len(req.EncryptedValue),
				"output_length":    len(doubleEncryptedHex),
				"company_id":       companyID,
			},
		}

		if err := db.CreateAuditLog(auditLog); err != nil {
			log.Printf("Failed to create audit log: %v", err)
			// Don't fail the request if audit logging fails
		}
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := EncryptResponse{
		Success: false,
		Message: message,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
