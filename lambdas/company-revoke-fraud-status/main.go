package main

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type RevokeFraudStatusRequest struct {
	FraudID   int    `json:"fraud_id"`   // ID of the fraud data record to revoke
	RevokedBy string `json:"revoked_by"` // User revoking the record
	Reason    string `json:"reason"`     // Optional reason for revocation
}

type RevokeFraudStatusResponse struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	FraudData *database.FraudData    `json:"fraud_data,omitempty"`
	RevokedAt string                 `json:"revoked_at,omitempty"`
	Status    string                 `json:"status,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-revoke-fraud-status")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req RevokeFraudStatusRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.FraudID == 0 || req.RevokedBy == "" {
		log.Println("Missing required fields")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: fraud_id, revoked_by")
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()

	// Get the fraud data record to verify it exists and belongs to this institution
	fraudData, err := db.GetFraudDataByID(req.FraudID)
	if err != nil {
		log.Printf("Failed to get fraud data: %v", err)
		return createErrorResponse(http.StatusNotFound, "Fraud data not found")
	}

	// Check if the fraud data belongs to this institution
	if fraudData.InstitutionID != institutionID {
		log.Printf("Fraud data belongs to institution %d, but request is from institution %d", fraudData.InstitutionID, institutionID)
		return createErrorResponse(http.StatusForbidden, "Access denied: fraud data does not belong to this institution")
	}

	// Check if the record is already inactive
	if fraudData.RowStatus != "active" {
		log.Printf("Fraud data record is already inactive: %s", fraudData.RowStatus)
		return createErrorResponse(http.StatusBadRequest, "Fraud data record is already inactive")
	}

	// Revoke the fraud data (mark as inactive)
	// If the fraud data has an association, revoke all related records
	if fraudData.AssociationID != nil {
		log.Printf("Fraud data has association ID %d, revoking all related records", *fraudData.AssociationID)
		err = db.RevokeFraudDataByAssociationID(*fraudData.AssociationID, req.RevokedBy)
		if err != nil {
			log.Printf("Failed to revoke fraud data by association: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to revoke fraud data")
		}
		log.Printf("Successfully revoked all fraud data records with association ID %d", *fraudData.AssociationID)
	} else {
		// Revoke only the single fraud data record
		err = db.RevokeFraudData(req.FraudID, req.RevokedBy)
		if err != nil {
			log.Printf("Failed to revoke fraud data: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to revoke fraud data")
		}
	}

	// Get the updated fraud data
	updatedFraudData, err := db.GetFraudDataByID(req.FraudID)
	if err != nil {
		log.Printf("Failed to get updated fraud data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to retrieve updated fraud data")
	}

	// Create audit log entry
	auditMetadata := map[string]interface{}{
		"fraud_id":        req.FraudID,
		"identifier":      fraudData.Identifier,
		"identifier_type": fraudData.IdentifierType,
		"old_status":      fraudData.Status,
		"old_fraud_type":  fraudData.FraudType,
		"revocation_reason": req.Reason,
	}
	
	// Add association information if present
	if fraudData.AssociationID != nil {
		auditMetadata["association_id"] = *fraudData.AssociationID
		auditMetadata["revoked_by_association"] = true
	} else {
		auditMetadata["revoked_by_association"] = false
	}

	auditLog := &database.AuditLog{
		EventType:     "fraud_data_revoked",
		InstitutionID: &institutionID,
		UserID:        req.RevokedBy,
		Metadata:      auditMetadata,
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	// Prepare response
	response := RevokeFraudStatusResponse{
		Success:   true,
		Message:   "Fraud data revoked successfully",
		FraudData: updatedFraudData,
		RevokedAt: time.Now().Format("2006-01-02 15:04:05"),
		Status:    "Success",
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := RevokeFraudStatusResponse{
		Success: false,
		Message: message,
		Status:  "Failed",
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "DELETE, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
} 