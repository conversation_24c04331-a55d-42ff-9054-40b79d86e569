"""
Lambda 2: Message Analysis Function
Processes batches from SMS Batch Queue, analyzes messages using OpenAI API,
stores results in database, and sends completion signals.
"""
import json
import logging
import os
from typing import Dict, List, Any
import asyncio

# Import from shared layer
from database import db_manager
from utils import (
    setup_logging, call_openai_api, send_sqs_message,
    get_environment_variable, validate_message_format,
    create_error_response, create_success_response
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

def lambda_handler(event, context):
    """
    Main Lambda handler for message analysis
    
    Expected SQS message format:
    {
        "customer_id": "customer_123",
        "message_ids": ["msg_id_1", "msg_id_2", ...],
        "batch_id": "batch_uuid",
        "batch_number": 1,
        "total_batches": 5
    }
    """
    try:
        logger.info(f"Processing {len(event.get('Records', []))} SQS messages")
        
        # Process each SQS record
        for record in event.get('Records', []):
            try:
                # Use asyncio to handle async operations
                asyncio.run(process_sqs_record(record))
            except Exception as e:
                logger.error(f"Failed to process SQS record: {str(e)}")
                # Continue processing other records
                continue
        
        return create_success_response(message="Message analysis completed successfully")
    
    except Exception as e:
        logger.error(f"Lambda execution failed: {str(e)}")
        return create_error_response(f"Lambda execution failed: {str(e)}")

async def process_sqs_record(record: Dict[str, Any]):
    """Process a single SQS record"""
    try:
        # Parse SQS message
        message_body = json.loads(record['body'])
        logger.info(f"Processing batch: {message_body}")
        
        # Validate message format
        if not validate_message_format(message_body):
            raise ValueError("Invalid message format")
        
        customer_id = message_body['customer_id']
        message_ids = message_body.get('message_ids', [])
        batch_id = message_body.get('batch_id')
        batch_number = message_body.get('batch_number', 1)
        total_batches = message_body.get('total_batches', 1)
        
        if not message_ids:
            raise ValueError("message_ids is required and cannot be empty")
        
        # Update batch status to processing
        if batch_id:
            db_manager.update_batch_status(batch_id, 'processing')
        
        # Log processing start
        db_manager.log_processing_event(
            customer_id=customer_id,
            batch_id=batch_id,
            log_level='INFO',
            message=f"Started message analysis for batch {batch_number}/{total_batches}",
            metadata={
                'message_count': len(message_ids),
                'batch_id': batch_id
            }
        )
        
        # Fetch messages from database
        logger.info(f"Fetching {len(message_ids)} messages from database")
        messages_data = db_manager.get_messages_by_ids(message_ids)
        
        if not messages_data:
            raise ValueError("No messages found for the given IDs")
        
        # Prepare messages for OpenAI analysis
        messages_for_analysis = [msg['message_text'] for msg in messages_data]
        
        # Get OpenAI API key
        openai_api_key = get_environment_variable('OPENAI_API_KEY', required=True)
        
        # Call OpenAI API for analysis
        logger.info(f"Analyzing {len(messages_for_analysis)} messages with OpenAI")
        analysis_results = await call_openai_api(messages_for_analysis, openai_api_key)
        
        # Store analysis results in database
        successful_analyses = 0
        failed_analyses = 0
        
        for i, (message_data, analysis_result) in enumerate(zip(messages_data, analysis_results)):
            message_id = message_data['message_id']
            
            try:
                if analysis_result['success']:
                    # Store successful analysis
                    db_manager.update_message_analysis(
                        message_id=str(message_id),
                        analysis_result=analysis_result['analysis'],
                        classification={
                            'openai_analysis': True,
                            'analysis_timestamp': analysis_result.get('timestamp'),
                            'confidence': analysis_result['analysis'].get('confidence', 0.8)
                        }
                    )
                    successful_analyses += 1
                    logger.debug(f"Stored analysis for message {message_id}")
                else:
                    # Store failed analysis with error info
                    db_manager.update_message_analysis(
                        message_id=str(message_id),
                        analysis_result={
                            'error': analysis_result['error'],
                            'success': False
                        },
                        classification={
                            'openai_analysis': False,
                            'error': analysis_result['error']
                        }
                    )
                    failed_analyses += 1
                    logger.warning(f"Failed to analyze message {message_id}: {analysis_result['error']}")
            
            except Exception as e:
                logger.error(f"Failed to store analysis for message {message_id}: {str(e)}")
                failed_analyses += 1
        
        # Update batch status
        if batch_id:
            if failed_analyses == 0:
                db_manager.update_batch_status(batch_id, 'completed')
            else:
                error_msg = f"Failed to analyze {failed_analyses} out of {len(message_ids)} messages"
                db_manager.update_batch_status(batch_id, 'failed', error_msg)
        
        # Log batch completion
        db_manager.log_processing_event(
            customer_id=customer_id,
            batch_id=batch_id,
            log_level='INFO',
            message=f"Completed analysis for batch {batch_number}/{total_batches}",
            metadata={
                'successful_analyses': successful_analyses,
                'failed_analyses': failed_analyses,
                'total_messages': len(message_ids)
            }
        )
        
        # Send completion signal
        await send_completion_signal(customer_id)
        
        logger.info(f"Successfully processed batch {batch_number}/{total_batches} for customer {customer_id}")
    
    except Exception as e:
        logger.error(f"Failed to process SQS record: {str(e)}")
        
        # Update batch status to failed
        try:
            message_body = json.loads(record['body'])
            batch_id = message_body.get('batch_id')
            customer_id = message_body.get('customer_id')
            
            if batch_id:
                db_manager.update_batch_status(batch_id, 'failed', str(e))
            
            if customer_id:
                db_manager.log_processing_event(
                    customer_id=customer_id,
                    batch_id=batch_id,
                    log_level='ERROR',
                    message=f"Message analysis failed: {str(e)}",
                    metadata={'error_type': type(e).__name__}
                )
        except:
            pass
        
        raise

async def send_completion_signal(customer_id: str):
    """Send completion signal to Completion Queue"""
    try:
        completion_queue_url = get_environment_variable('COMPLETION_QUEUE_URL', required=True)
        
        # Prepare completion message
        completion_message = {
            'customer_id': customer_id,
            'event_type': 'batch_completed',
            'timestamp': asyncio.get_event_loop().time()
        }
        
        # Send to Completion Queue
        message_id = send_sqs_message(completion_queue_url, completion_message)
        
        logger.info(f"Sent completion signal for customer {customer_id}: {message_id}")
        
        # Log completion signal
        db_manager.log_processing_event(
            customer_id=customer_id,
            log_level='INFO',
            message=f"Sent completion signal for customer {customer_id}",
            metadata={'sqs_message_id': message_id}
        )
    
    except Exception as e:
        logger.error(f"Failed to send completion signal: {str(e)}")
        # Don't raise here as the main processing was successful
