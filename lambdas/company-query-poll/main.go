package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/google/uuid"
	"secure_double_blind_ec/pkg/database"
)

type PollRequest struct {
	TaskID string `json:"task_id"`
}

type PollResponse struct {
	Success     bool                    `json:"success"`
	Message     string                  `json:"message"`
	Status      string                  `json:"status,omitempty"`
	TaskID      string                  `json:"task_id,omitempty"`
	Response    map[string]interface{}  `json:"response,omitempty"`
	Steps       []map[string]interface{} `json:"steps,omitempty"`
	Error       *string                 `json:"error,omitempty"`
	Progress    map[string]interface{}  `json:"progress,omitempty"`
	QueryID     string                  `json:"query_id,omitempty"`
	QueryDate   string                  `json:"query_date,omitempty"`
	Summary     map[string]interface{}  `json:"summary,omitempty"`
	Processes   []map[string]interface{} `json:"processes,omitempty"`
	Timeline    []map[string]interface{} `json:"timeline,omitempty"`
	Institution map[string]interface{}  `json:"institution,omitempty"`
	Identifiers []map[string]interface{} `json:"identifiers,omitempty"`
	Stats       map[string]interface{}  `json:"stats,omitempty"`
}

// StepInfo represents information about a workflow step
type StepInfo struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Timestamp   string `json:"timestamp,omitempty"`
	Progress    int    `json:"progress"`
	Total       int    `json:"total,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-query-poll")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	// Create company ID from institution ID
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req PollRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.TaskID == "" {
		log.Println("Missing required field: task_id")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: task_id")
	}

	// Parse task ID string to UUID
	taskID, err := uuid.Parse(req.TaskID)
	if err != nil {
		log.Printf("Invalid task ID format: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid task ID format")
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()

	// Get task log
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		log.Printf("Failed to get task log: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to get task log")
	}

	// Check if task belongs to this company
	if taskLog.CreatedBy != companyID {
		log.Printf("Task %s does not belong to company %s", req.TaskID, companyID)
		return createErrorResponse(http.StatusForbidden, "You do not have permission to access this task")
	}

	// Create audit log entry for query polling
	auditLog := &database.AuditLog{
		EventType:     "query_polled",
		InstitutionID: &institutionID,
		UserID:        "system", // Since no user ID is provided in the request
		Metadata: map[string]interface{}{
			"task_id":     taskLog.TaskID.String(),
			"task_status": taskLog.Status,
			"task_type":   taskLog.TaskType,
			"steps_count": len(taskLog.Steps),
			"company_id":  companyID,
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	// Format steps for the UI
	formattedSteps, progress := formatStepsForUI(taskLog.Steps, taskLog.Status)
	
	// Generate query ID in format QRY-YYYYMMDD-XXX
	currentDate := time.Now().Format("********")
	queryID := fmt.Sprintf("QRY-%s-%s", currentDate, taskID.String()[:8])
	
	// Create processes data based on steps
	processes := []map[string]interface{}{}
	
	// Define the workflow steps in order
	workflowSteps := []struct {
		ID          int
		Name        string
		Description string
	}{
		{1, "secure_query_creation", "Secure Query Creation"},
		{2, "encrypted_partner_communication", "Encrypted Partner Communication"},
		{3, "encrypted_consortium_lookup", "Encrypted Consortium Lookup"},
		{4, "report_generation", "Report Generation"},
	}
	
	// Create a map of step name to step info from task log
	stepMap := make(map[string]map[string]interface{})
	
	// Initialize all steps as pending
	for _, step := range workflowSteps {
		stepMap[step.Name] = map[string]interface{}{
			"id":          step.ID,
			"name":        step.Description,
			"description": getStepDescription(step.Name),
			"status":      "Pending",
		}
	}
	
	// Update steps based on actual progress from task log
	for _, step := range taskLog.Steps {
		stepName, ok := step["step"].(string)
		if !ok {
			continue
		}
		
		// Skip unknown steps
		if _, exists := stepMap[stepName]; !exists {
			continue
		}
		
		// Update step info
		status, _ := step["status"].(string)
		timestamp, _ := step["timestamp"].(string)
		
		// Convert status to UI-friendly format
		uiStatus := "Pending"
		if status == "completed" {
			uiStatus = "Success"
		} else if status == "failed" {
			uiStatus = "Failed"
		} else if status == "in_progress" {
			uiStatus = "In Progress"
		}
		
		stepMap[stepName]["status"] = uiStatus
		
		if timestamp != "" {
			// Parse timestamp to get a consistent format
			t, err := time.Parse(time.RFC3339, timestamp)
			if err == nil {
				stepMap[stepName]["timestamp"] = t.Format("2006/01/02 15:04")
			} else {
				stepMap[stepName]["timestamp"] = timestamp
			}
		}
	}
	
	// Convert map to ordered array
	for _, step := range workflowSteps {
		stepInfo := stepMap[step.Name]
		
		// Add progress information for specific steps
		if step.Name == "encrypted_partner_communication" && stepInfo["status"] == "Success" {
			stepInfo["progress"] = 5
			stepInfo["total"] = 5
		} else if step.Name == "encrypted_consortium_lookup" && stepInfo["status"] == "Success" {
			stepInfo["progress"] = 100
		}
		
		processes = append(processes, stepInfo)
	}
	
	// Create timeline data based on response if available
	var timeline []map[string]interface{}
	var identifiers []map[string]interface{}
	var stats map[string]interface{}
	var summary map[string]interface{}
	
	// If we have response data, use it to populate the timeline and other fields
	if taskLog.Response != nil {
		// Extract fraud data from response if available
		if fraudDataInterface, ok := taskLog.Response["fraud_data"]; ok {
			if fraudDataList, ok := fraudDataInterface.([]interface{}); ok {
				// Calculate summary statistics
				totalMatches := len(fraudDataList)
				confirmedCount := 0
				suspectedCount := 0
				revokedCount := 0
				
				// Organize identifiers by type
				identifiersByType := make(map[string][]map[string]interface{})
				
				// Create timeline entries from fraud data
				timeline = []map[string]interface{}{}
				
				// Track earliest and latest dates
				var firstMatchDate, latestMatchDate time.Time
				var hasFirstDate bool
				
				for i, fraudDataItem := range fraudDataList {
					if fraudData, ok := fraudDataItem.(map[string]interface{}); ok {
						// Extract data
						status, _ := fraudData["status"].(string)
						identifierType, _ := fraudData["identifier_type"].(string)
						institutionID, _ := fraudData["institution_id"].(float64)
						createdAt, _ := fraudData["created_at"].(string)
						
						// Count by status
						switch status {
						case "Confirmed":
							confirmedCount++
						case "Suspected":
							suspectedCount++
						case "Absolved":
							revokedCount++
						}
						
						// Parse created_at to get date and time
						t, err := time.Parse(time.RFC3339, createdAt)
						if err != nil {
							// If we can't parse the date, use current time
							t = time.Now()
						}
						
						// Track earliest and latest dates
						if !hasFirstDate {
							firstMatchDate = t
							latestMatchDate = t
							hasFirstDate = true
						} else {
							if t.Before(firstMatchDate) {
								firstMatchDate = t
							}
							if t.After(latestMatchDate) {
								latestMatchDate = t
							}
						}
						
						dateStr := t.Format("2006/01/02")
						timeStr := t.Format("15:04")
						
						// Determine institution name based on ID
						institutionName := "Unknown Institution"
						if institutionNameStr, ok := fraudData["institution_name"].(string); ok && institutionNameStr != "" {
							institutionName = institutionNameStr
						} else {
							// Fallback based on ID
							switch int(institutionID) {
							case 1:
								institutionName = "Private Bank"
							case 2:
								institutionName = "PSU Bank"
							case 3:
								institutionName = "NBFC"
							}
						}
						
						// Determine event type based on identifier type
						eventType := "Fraud"
						if identifierType == "Email" {
							eventType = "Victim"
						}
						
						// Get incident ID if available, otherwise use fraud data ID
						incidentID, _ := fraudData["incident_id"].(string)
						if incidentID == "" {
							if fraudDataID, ok := fraudData["id"].(float64); ok {
								incidentID = fmt.Sprintf("INS-%s-%d", t.Format("********"), int(fraudDataID))
							} else {
								incidentID = fmt.Sprintf("INS-%s-%03d", t.Format("********"), i+1)
							}
						}
						
						// Create timeline entry
						timelineEntry := map[string]interface{}{
							"date": dateStr,
							"time": timeStr,
							"institution": institutionName,
							"event_id": incidentID,
							"identifier_type": identifierType,
							"status": status,
							"event_type": eventType,
						}
						
						timeline = append(timeline, timelineEntry)
						
						// Group by identifier type
						identifier, _ := fraudData["identifier"].(string)
						if _, exists := identifiersByType[identifierType]; !exists {
							identifiersByType[identifierType] = []map[string]interface{}{}
						}
						
						identifiersByType[identifierType] = append(identifiersByType[identifierType], map[string]interface{}{
							"id": identifier,
							"type": identifierType,
							"status": status,
						})
					}
				}
				
				// Create identifiers list for response
				identifiers = []map[string]interface{}{}
				for idType, ids := range identifiersByType {
					// For each identifier type, create an entry
					identifiers = append(identifiers, map[string]interface{}{
						"type": idType,
						"values": ids,
						"total": len(ids),
						"past_1y": 0,  // These would need actual calculation based on dates
						"past_1m": 0,
					})
				}
				
				// Format dates for display
				firstMatchDateStr := firstMatchDate.Format("02/06/2006")
				latestMatchDateStr := latestMatchDate.Format("02/06/2006")
				
				// Create stats data
				stats = map[string]interface{}{
					"total_matches": totalMatches,
					"confirmed": confirmedCount,
					"suspected": suspectedCount,
					"revoked": revokedCount,
					"first_match": firstMatchDateStr,
					"latest_match": latestMatchDateStr,
				}
				
				// Create summary data
				summary = map[string]interface{}{
					"institution": map[string]interface{}{
						"name": "NPCI",
						"type": "Regulator",
					},
					"status": taskLog.Status,
					"date": time.Now().Format("02/01/2006"),
					"report_id": fmt.Sprintf("RPT-%s-NPC", currentDate),
					"identifiers": len(identifiersByType),
				}
			}
		}
	}
	
	// If we don't have timeline data yet, create sample data
	if timeline == nil {
		timeline = []map[string]interface{}{
			{
				"date": time.Now().Format("2006/01/02"),
				"time": time.Now().Format("15:04"),
				"institution": "Private Bank",
				"event_id": "INS-********-001",
				"identifier_type": "Device",
				"status": "Confirmed",
				"event_type": "Fraud",
			},
			{
				"date": time.Now().Format("2006/01/02"),
				"time": time.Now().Format("15:04"),
				"institution": "PSU Bank",
				"event_id": "INS-********-002",
				"identifier_type": "Email",
				"status": "Suspected",
				"event_type": "Victim",
			},
		}
	}
	
	// If we don't have summary data yet, create sample data
	if summary == nil {
		summary = map[string]interface{}{
			"institution": map[string]interface{}{
				"name": "NPCI",
				"type": "Regulator",
			},
			"status": taskLog.Status,
			"date": time.Now().Format("02/01/2006"),
			"report_id": fmt.Sprintf("RPT-%s-NPC", currentDate),
			"identifiers": 2,
		}
	}
	
	// If we don't have stats data yet, create sample data
	if stats == nil {
		stats = map[string]interface{}{
			"total_matches": 4,
			"confirmed": 2,
			"suspected": 1,
			"revoked": 1,
			"first_match": time.Now().Format("02/06/2024"),
			"latest_match": time.Now().Format("02/06/2024"),
		}
	}
	
	// If we don't have identifiers data yet, create sample data
	if identifiers == nil {
		identifiers = []map[string]interface{}{
			{
				"type": "Email",
				"values": []map[string]interface{}{
					{"id": "<EMAIL>", "type": "Email", "status": "Confirmed"},
				},
				"total": 1,
				"past_1y": 0,
				"past_1m": 0,
			},
			{
				"type": "Device",
				"values": []map[string]interface{}{
					{"id": "DEVICE123", "type": "Device", "status": "Suspected"},
				},
				"total": 1,
				"past_1y": 0,
				"past_1m": 0,
			},
		}
	}

	// Prepare response
	response := PollResponse{
		Success:     true,
		Message:     fmt.Sprintf("Task status: %s", taskLog.Status),
		Status:      taskLog.Status,
		TaskID:      taskLog.TaskID.String(),
		Response:    taskLog.Response,
		Steps:       formattedSteps,
		Error:       taskLog.Error,
		Progress:    progress,
		QueryID:     queryID,
		QueryDate:   time.Now().Format("02/01/2006"),
		Summary:     summary,
		Timeline:    timeline,
		Processes:   processes,
		Institution: map[string]interface{}{
			"name": "NPCI",
			"type": "Regulator",
		},
		Identifiers: identifiers,
		Stats:       stats,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

// formatStepsForUI formats the steps for the UI and calculates progress
func formatStepsForUI(steps []map[string]interface{}, taskStatus string) ([]map[string]interface{}, map[string]interface{}) {
	// Define the workflow steps in order
	workflowSteps := []struct {
		ID          int
		Name        string
		Description string
	}{
		{1, "secure_query_creation", "Secure Query Creation"},
		{2, "encrypted_partner_communication", "Encrypted Partner Communication"},
		{3, "encrypted_consortium_lookup", "Encrypted Consortium Lookup"},
		{4, "report_generation", "Report Generation"},
	}

	// Create a map of step name to step info
	stepMap := make(map[string]map[string]interface{})
	
	// Initialize all steps as pending
	for _, step := range workflowSteps {
		stepMap[step.Name] = map[string]interface{}{
			"id":          step.ID,
			"name":        step.Name,
			"description": step.Description,
			"status":      "pending",
		}
	}
	
	// Update steps based on actual progress
	for _, step := range steps {
		stepName, ok := step["step"].(string)
		if !ok {
			continue
		}
		
		// Skip unknown steps
		if _, exists := stepMap[stepName]; !exists {
			continue
		}
		
		// Update step info
		stepMap[stepName]["status"] = step["status"]
		
		if timestamp, ok := step["timestamp"].(string); ok {
			stepMap[stepName]["timestamp"] = timestamp
		}
	}
	
	// Convert map to ordered array
	formattedSteps := make([]map[string]interface{}, len(workflowSteps))
	completedSteps := 0
	totalSteps := len(workflowSteps)
	
	for i, step := range workflowSteps {
		stepInfo := stepMap[step.Name]
		formattedSteps[i] = stepInfo
		
		// Count completed steps
		if stepInfo["status"] == "completed" {
			completedSteps++
		}
	}
	
	// Calculate overall progress
	var progressPercentage int
	if taskStatus == "completed" {
		progressPercentage = 100
	} else if taskStatus == "failed" {
		// If failed, show progress up to the failure point
		progressPercentage = (completedSteps * 100) / totalSteps
	} else {
		// For in-progress tasks, calculate percentage
		progressPercentage = (completedSteps * 100) / totalSteps
	}
	
	progress := map[string]interface{}{
		"completed": completedSteps,
		"total":     totalSteps,
		"percentage": progressPercentage,
	}
	
	return formattedSteps, progress
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	// Generate a query ID in format QRY-YYYYMMDD-XXX
	currentDate := time.Now().Format("********")
	queryID := fmt.Sprintf("QRY-%s-ERR%03d", currentDate, statusCode)
	
	response := PollResponse{
		Success:     false,
		Message:     message,
		QueryID:     queryID,
		QueryDate:   time.Now().Format("02/01/2006"),
		Summary: map[string]interface{}{
			"status": "Failed",
			"error": message,
		},
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}

// Helper function to get a user-friendly description for a step
func getStepDescription(stepName string) string {
	switch stepName {
	case "secure_query_creation":
		return "Query securely created and signed by institution"
	case "encrypted_partner_communication":
		return "Query encrypted and sent to partner institutions"
	case "encrypted_consortium_lookup":
		return "Consortium-wide lookup and validation"
	case "report_generation":
		return "Final report generated and delivered"
	default:
		return "Processing step"
	}
}
