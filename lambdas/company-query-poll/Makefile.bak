# Include the universal Makefile
include ./universal.mk

# Function-specific variables
FUNCTION_NAME=CompanyAQueryPollFunction
LAMBDA_PATH=lambdas/company-query-poll

# Targets for SAM build
.PHONY: build-CompanyAQueryPollFunction build-CompanyBQueryPollFunction build-CompanyCQueryPollFunction

build-CompanyAQueryPollFunction: build
build-CompanyBQueryPollFunction: build
build-CompanyCQueryPollFunction: build

clean:
	@echo "Cleaning build artifacts..."
	rm -f bootstrap
