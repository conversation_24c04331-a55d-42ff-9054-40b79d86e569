package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sqs"
	"github.com/google/uuid"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type AsyncQueryRequest struct {
	Identifier string `json:"identifier"` // Plain text identifier to query
}

type AsyncQueryResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	TaskID    string `json:"task_id,omitempty"`
}

type TaskMessage struct {
	TaskID       string `json:"task_id"`
	CompanyID    string `json:"company_id"`
	Identifier   string `json:"identifier"`
	BlindedValue string `json:"blinded_value"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-query-async")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	// Create company ID from institution ID
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req AsyncQueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.Identifier == "" {
		log.Println("Missing required field: identifier")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: identifier")
	}

	// Initialize blinding service for this company
	log.Printf("Initializing blinding service for company: %s", companyID)
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initialize blinding service")
	}

	// Blind the identifier for query
	log.Printf("Blinding identifier for query: %s", req.Identifier)
	blindedElement, err := blindingService.BlindData(req.Identifier)
	if err != nil {
		log.Printf("Failed to blind identifier: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to blind identifier")
	}

	// Convert blinded element to hex string
	blindedBytes, err := blindedElement.MarshalBinary()
	if err != nil {
		log.Printf("Failed to serialize blinded element: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize blinded element")
	}
	blindedHex := fmt.Sprintf("%x", blindedBytes)

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()

	// Create task log entry
	taskID := uuid.New()
	now := time.Now()
	
	// Define all workflow steps with initial status
	steps := []map[string]interface{}{
		{
			"step":        "secure_query_creation",
			"description": "Query securely created and signed by institution",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "completed",
		},
		{
			"step":        "encrypted_partner_communication",
			"description": "Query encrypted and sent to partner institutions",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
		{
			"step":        "encrypted_consortium_lookup",
			"description": "Consortium-wide lookup and validation",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
		{
			"step":        "report_generation",
			"description": "Final report generated and delivered",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
	}
	
	taskLog := &database.TaskLog{
		TaskID:             taskID,
		TaskType:           "query",
		BlindedIdentifiers: []database.BlindedIdentifier{{Identifier: blindedHex, Type: "unknown"}},
		InstitutionID:      &institutionID,
		Status:             "pending",
		CreatedBy:          companyID,
		StartTime:          &now,
		Steps:              steps,
	}

	if err := db.CreateTaskLog(taskLog); err != nil {
		log.Printf("Failed to create task log: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to create task log")
	}

	// Send message to SQS queue for processing
	err = sendTaskToQueue(taskID.String(), companyID, req.Identifier, blindedHex)
	if err != nil {
		log.Printf("Failed to send task to queue: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initiate task processing")
	}

	// Prepare response
	response := AsyncQueryResponse{
		Success: true,
		Message: "Query task created successfully. Use the task_id to poll for results.",
		TaskID:  taskID.String(),
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusAccepted,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func sendTaskToQueue(taskID, companyID, identifier, blindedValue string) error {
	// Get queue URL from environment variable
	queueURL := os.Getenv("TASK_QUEUE_URL")
	if queueURL == "" {
		return fmt.Errorf("TASK_QUEUE_URL environment variable not set")
	}

	// Create AWS session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("ap-south-1"), // Use the same region as your stack
	})
	if err != nil {
		return fmt.Errorf("failed to create AWS session: %v", err)
	}

	// Create SQS service client
	sqsClient := sqs.New(sess)

	// Create task message
	task := TaskMessage{
		TaskID:       taskID,
		CompanyID:    companyID,
		Identifier:   identifier,
		BlindedValue: blindedValue,
	}

	// Marshal message to JSON
	messageBody, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal task message: %v", err)
	}

	// Send message to SQS queue
	_, err = sqsClient.SendMessage(&sqs.SendMessageInput{
		QueueUrl:    aws.String(queueURL),
		MessageBody: aws.String(string(messageBody)),
	})
	if err != nil {
		return fmt.Errorf("failed to send message to SQS queue: %v", err)
	}

	log.Printf("Task %s sent to queue for processing", taskID)
	return nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := AsyncQueryResponse{
		Success: false,
		Message: message,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
