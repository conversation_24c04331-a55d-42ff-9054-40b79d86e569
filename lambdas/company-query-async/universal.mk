# Universal Makefile for all Lambda Functions
# Usage: include this file from each Lambda function's Makefile

.PHONY: build clean

# Go build parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

# Default values
FUNCTION_NAME?=UnknownFunction
LAMBDA_PATH?=unknown

# If ARTIFACTS_DIR is not set by SAM, use the current directory
ARTIFACTS_DIR?=.

# Determine project root (2 directories up from the lambda directory)
PROJECT_ROOT := $(shell cd ../.. && pwd)

build:
	@echo "Building $(FUNCTION_NAME) function..."
	cd $(PROJECT_ROOT) && \
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
	go build -ldflags="-s -w" -o $(ARTIFACTS_DIR)/bootstrap $(LAMBDA_PATH)/main.go
	@echo "$(FUNCTION_NAME) built successfully!"

clean:
	@echo "Cleaning build artifacts for $(FUNCTION_NAME)..."
	rm -f bootstrap
	@echo "Clean completed!"
