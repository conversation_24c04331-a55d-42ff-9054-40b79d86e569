package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type DecryptRequest struct {
	DoubleEncryptedValue string `json:"double_encrypted_value"` // Hex-encoded double encrypted value
}

type DecryptResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	DecryptedValue string `json:"decrypted_value,omitempty"` // Hex-encoded decrypted value
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-decrypt")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	// Create company ID from institution ID
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req DecryptRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.DoubleEncryptedValue == "" {
		log.Println("Missing required field: double_encrypted_value")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: double_encrypted_value")
	}

	// Initialize blinding service for this company
	log.Printf("Initializing blinding service for company: %s", companyID)
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initialize blinding service")
	}

	// Decode hex string to bytes
	doubleEncryptedBytes, err := hex.DecodeString(req.DoubleEncryptedValue)
	if err != nil {
		log.Printf("Failed to decode hex string: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid double_encrypted_value format")
	}

	// Convert bytes to group element
	doubleEncryptedElement := blindingService.GetGroup().NewElement()
	if err := doubleEncryptedElement.UnmarshalBinary(doubleEncryptedBytes); err != nil {
		log.Printf("Failed to unmarshal group element: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid double_encrypted_value data")
	}

	// Decrypt the double encrypted value
	log.Println("Decrypting double encrypted value")
	decrypted, err := blindingService.DecryptToGetOtherCompanyValue(doubleEncryptedElement)
	if err != nil {
		log.Printf("Failed to decrypt value: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to decrypt value")
	}

	// Convert decrypted element to hex string
	decryptedBytes, err := decrypted.MarshalBinary()
	if err != nil {
		log.Printf("Failed to serialize decrypted element: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize decrypted element")
	}
	decryptedHex := hex.EncodeToString(decryptedBytes)

	// Prepare response
	response := DecryptResponse{
		Success:        true,
		Message:        "Value decrypted successfully",
		DecryptedValue: decryptedHex,
	}

	// Create audit log entry for decryption operation
	db, err := database.New()
	if err == nil {
		defer db.Close()
		
		auditLog := &database.AuditLog{
			EventType:     "data_decrypted",
			InstitutionID: &institutionID,
			UserID:        "system", // Since no user ID is provided in the request
			Metadata: map[string]interface{}{
				"operation":        "double_decryption",
				"input_length":     len(req.DoubleEncryptedValue),
				"output_length":    len(decryptedHex),
				"company_id":       companyID,
			},
		}

		if err := db.CreateAuditLog(auditLog); err != nil {
			log.Printf("Failed to create audit log: %v", err)
			// Don't fail the request if audit logging fails
		}
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := DecryptResponse{
		Success: false,
		Message: message,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
