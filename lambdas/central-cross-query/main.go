package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
	"log"
)

type CrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"` // Hex-encoded blinded identifier
	RequestingCompany string `json:"requesting_company"` // Company making the request
}

type CompanyEncryptRequest struct {
	EncryptedValue string `json:"encrypted_value"` // Hex-encoded encrypted value
}

type CrossQueryResponse struct {
	Success       bool              `json:"success"`
	Message       string            `json:"message"`
	BlindedValues map[string]string `json:"blinded_values,omitempty"` // Map of company_id -> double_encrypted_value (hex)
}

// Result struct to hold the results of parallel API calls
type encryptResult struct {
	companyID           string
	doubleEncryptedValue string
	err                  error
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: central-cross-query")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req CrossQueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.BlindedIdentifier == "" || req.RequestingCompany == "" {
		log.Printf("Missing required fields: blinded_identifier or requesting_company")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields")
	}
	log.Printf("Base blinded identifier: %s", req.BlindedIdentifier)

	// Use the provided blinded identifier hex string directly
	// No need to recalculate since it's already provided as hex-encoded
	baseEncryptedHex := req.BlindedIdentifier

	// Map to store double-encrypted values from all companies
	blindedValues := make(map[string]string)

	// Initialize database connection
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()

	// Get institutions with fraud data from the database
	institutionsWithFraudData, err := db.GetInstitutionsWithFraudData()
	if err != nil {
		log.Printf("Failed to get institutions with fraud data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to get institutions with fraud data")
	}
	
	log.Printf("Found %d institutions with fraud data", len(institutionsWithFraudData))
	
	// Create a map for quick lookup of institutions with fraud data
	fraudDataInstitutionMap := make(map[int]bool)
	for _, id := range institutionsWithFraudData {
		fraudDataInstitutionMap[id] = true
	}

	// Extract requesting institution ID from company ID (e.g., "company_2" -> 2)
	requestingInstitutionID := 0
	if parts := strings.Split(req.RequestingCompany, "_"); len(parts) > 1 {
		fmt.Sscanf(parts[1], "%d", &requestingInstitutionID)
	}

	// Get all institutions from the database
	institutions, err := db.ListInstitutions()
	if err != nil {
		log.Printf("Failed to list institutions: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to list institutions")
	}

	// For the requesting company, add the original blinded identifier
	requestingCompanyID := fmt.Sprintf("company_%d", requestingInstitutionID)
	blindedValues[requestingCompanyID] = req.BlindedIdentifier

	// Create a wait group to wait for all goroutines to complete
	var wg sync.WaitGroup
	
	// Create a channel to collect results
	resultChan := make(chan encryptResult)
	
	// Count of institutions to query
	institutionsToQuery := 0

	// Process each institution that has encryption keys set AND has fraud data
	for _, institution := range institutions {
		// Skip institutions without encryption keys or with status inactive
		if institution.EncryptionKey == nil || institution.EncryptionSalt == nil || institution.Status != "active" {
			continue
		}

		// Skip institutions that don't have fraud data
		if !fraudDataInstitutionMap[institution.ID] {
			log.Printf("Skipping institution %s (ID: %d) - no fraud data", institution.InstitutionName, institution.ID)
			continue
		}

		// Convert institution ID to company ID format
		companyID := fmt.Sprintf("company_%d", institution.ID)
		
		// Skip the requesting company
		if institution.ID == requestingInstitutionID {
			log.Printf("Skipping requesting institution: %s (ID: %d)", institution.InstitutionName, institution.ID)
			continue
		}

		// Increment the counter for institutions to query
		institutionsToQuery++
		
		// Launch a goroutine for each institution
		wg.Add(1)
		go func(inst *database.Institution, cID string) {
			defer wg.Done()
			
			log.Printf("Calling encrypt API for institution: %s (ID: %d)", inst.InstitutionName, inst.ID)
			
			// Call the company's encrypt API
			encryptReq := CompanyEncryptRequest{
				EncryptedValue: baseEncryptedHex,
			}

			doubleEncryptedValue, err := callCompanyEncryptAPI(cID, encryptReq)
			
			// Send the result to the channel
			resultChan <- encryptResult{
				companyID:           cID,
				doubleEncryptedValue: doubleEncryptedValue,
				err:                  err,
			}
		}(institution, companyID)
	}
	
	// Start a goroutine to close the result channel once all workers are done
	go func() {
		wg.Wait()
		close(resultChan)
	}()
	
	// Collect results from the channel
	for result := range resultChan {
		if result.err != nil {
			// Log the error but continue with other companies
			log.Printf("Failed to get encryption from company %s: %v", result.companyID, result.err)
			continue
		}
		log.Printf("Received double-encrypted value from company %s", result.companyID)
		blindedValues[result.companyID] = result.doubleEncryptedValue
	}

	log.Printf("Completed %d parallel encryption requests", institutionsToQuery)

	// Create audit log entry for cross-company query
	auditLog := &database.AuditLog{
		EventType: "cross_company_query",
		UserID:    "system", // Since this is a central operation
		Metadata: map[string]interface{}{
			"requesting_company":     req.RequestingCompany,
			"companies_queried":      len(blindedValues),
			"total_institutions":     len(institutions),
			"institutions_with_data": len(institutionsWithFraudData),
			"blinded_identifier_length": len(req.BlindedIdentifier),
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	// Prepare response
	response := CrossQueryResponse{
		Success:       true,
		Message:       "Cross-company query completed successfully",
		BlindedValues: blindedValues,
	}
	log.Printf("Returning response: %+v", response)
	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func callCompanyEncryptAPI(companyID string, req CompanyEncryptRequest) (string, error) {
	baseAPIURL := os.Getenv("BASE_API_URL")
	if baseAPIURL == "" {
		// Fallback to older env var for backward compatibility
		baseAPIURL = os.Getenv("COMPANY_API_URL")
		if baseAPIURL == "" {
			return "", fmt.Errorf("BASE_API_URL environment variable not set")
		}
	}
	
	// Ensure the URL ends with a slash
	if !strings.HasSuffix(baseAPIURL, "/") {
		baseAPIURL += "/"
	}
	
	// Extract institution ID from company ID (e.g., "company_2" -> "2")
	institutionID := ""
	if parts := strings.Split(companyID, "_"); len(parts) > 1 {
		institutionID = parts[1]
	}
	
	if institutionID == "" {
		return "", fmt.Errorf("invalid company ID format: %s", companyID)
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	// Use the API format with path parameters
	url := fmt.Sprintf("%sinstitution/%s/encrypt", baseAPIURL, institutionID)
	log.Printf("Calling URL: %s", url)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to call company encrypt API: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read response body for more details
		var errorResponse []byte = make([]byte, 1024)
		n, _ := resp.Body.Read(errorResponse)
		return "", fmt.Errorf("company server returned status: %d, body: %s", resp.StatusCode, errorResponse[:n])
	}

	var response struct {
		Success        bool   `json:"success"`
		Message        string `json:"message"`
		EncryptedValue string `json:"encrypted_value"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %v", err)
	}

	if !response.Success {
		return "", fmt.Errorf("company encrypt API returned failure: %s", response.Message)
	}

	return response.EncryptedValue, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
