package main

import (
	"context"
	"encoding/json"
	"net/http"
	"log"
	"strconv"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type IdentifierInfo struct {
	Encoded string `json:"encoded"`
	Type    string `json:"type"`
}

type ListInsertsResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Inserts []database.InsertRecord `json:"inserts,omitempty"`
	Total   int                     `json:"total,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-list-inserts")

	// Parse query parameters
	var req database.ListInsertsRequest
	
	// Get institution_id from path parameter
	if instIDStr := request.PathParameters["institution_id"]; instIDStr != "" {
		if instID, err := strconv.Atoi(instIDStr); err == nil {
			req.InstitutionID = &instID
		} else {
			return createErrorResponse(http.StatusBadRequest, "Invalid institution_id in path")
		}
	} else {
		return createErrorResponse(http.StatusBadRequest, "institution_id is required in path")
	}

	// Parse arrays from query parameters
	if statusStr := request.QueryStringParameters["status"]; statusStr != "" {
		req.Status = strings.Split(statusStr, ",")
	}
	if identifierTypeStr := request.QueryStringParameters["identifier_type"]; identifierTypeStr != "" {
		req.IdentifierType = strings.Split(identifierTypeStr, ",")
	}
	if fraudTypeStr := request.QueryStringParameters["fraud_type"]; fraudTypeStr != "" {
		req.FraudType = strings.Split(fraudTypeStr, ",")
	}
	if fraudStatusStr := request.QueryStringParameters["fraud_status"]; fraudStatusStr != "" {
		req.FraudStatus = strings.Split(fraudStatusStr, ",")
	}

	// Parse sorting
	req.SortBy = request.QueryStringParameters["sort_by"]
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	req.SortOrder = request.QueryStringParameters["sort_order"]
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// Parse pagination
	if limitStr := request.QueryStringParameters["limit"]; limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			req.Limit = limit
		} else {
			req.Limit = 50
		}
	} else {
		req.Limit = 50
	}

	if offsetStr := request.QueryStringParameters["offset"]; offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			req.Offset = offset
		}
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	inserts, total, err := db.ListInsertsWithFilters(req)
	if err != nil {
		log.Printf("Failed to list inserts: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to list inserts")
	}

	// Create audit log entry for inserts listing
	auditLog := &database.AuditLog{
		EventType:     "inserts_listed",
		InstitutionID: req.InstitutionID,
		UserID:        "system", // Since no user ID is provided in the request
		Metadata: map[string]interface{}{
			"inserts_count":     len(inserts),
			"total_count":       total,
			"limit":             req.Limit,
			"offset":            req.Offset,
			"sort_by":           req.SortBy,
			"sort_order":        req.SortOrder,
			"filters_applied":   len(req.Status) + len(req.IdentifierType) + len(req.FraudType) + len(req.FraudStatus),
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	response := ListInsertsResponse{
		Success: true,
		Message: "Inserts fetched successfully",
		Inserts: inserts,
		Total:   total,
	}
	responseJSON, _ := json.Marshal(response)
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	responseJSON, _ := json.Marshal(errorResponse)
	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
} 