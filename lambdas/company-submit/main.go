package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
	"log"
	"math/rand"
)

type Identifier struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type SubmitRequest struct {
	Identifiers       []Identifier           `json:"identifiers"`
	Metadata          map[string]interface{} `json:"metadata"`
	InstitutionID     int                    `json:"institution_id"`
	Status            string                 `json:"status"`
	FraudType         string                 `json:"fraud_type"`
	CreatedBy         string                 `json:"created_by"`
}

type CentralStoreRequest struct {
	BlindedID       string                 `json:"blinded_id"`
	IdentifierType  string                 `json:"identifier_type"`
	Metadata        map[string]interface{} `json:"metadata"`
	InstitutionID   int                    `json:"institution_id"`
	CreatedBy       string                 `json:"created_by"`
	Status          string                 `json:"status"`
	FraudType       string                 `json:"fraud_type"`
	Identifiers     []IdentifierData       `json:"identifiers,omitempty"`
}

type IdentifierData struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type SubmitResponse struct {
	Success             bool                   `json:"success"`
	Message             string                 `json:"message"`
	RequestID           string                 `json:"request_id,omitempty"`
	InsertID            string                 `json:"insert_id,omitempty"`
	AssociationID       *int                   `json:"association_id,omitempty"`
	Identifiers         []Identifier           `json:"identifiers,omitempty"`
	BlindedIdentifiers  []map[string]string    `json:"blinded_identifiers,omitempty"`
	Institution         map[string]interface{} `json:"institution,omitempty"`
	Status              string                 `json:"status,omitempty"`
	FraudType           string                 `json:"fraud_type,omitempty"`
	FraudStatus         string                 `json:"fraud_status,omitempty"`
	CreatedDatetime     string                 `json:"created_datetime,omitempty"`
	LastUpdatedDatetime string                 `json:"last_updated_datetime,omitempty"`
	ResponseTime        int64                  `json:"response_time_ms,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	startTime := time.Now()
	log.Println("Handler started: company-submit")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters", startTime)
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id", startTime)
	}
	
	// Create company ID from institution ID
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req SubmitRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body", startTime)
	}
	
	// Override institution ID from path parameter
	req.InstitutionID = institutionID
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if len(req.Identifiers) == 0 || req.Status == "" || req.FraudType == "" || req.CreatedBy == "" {
		log.Println("Missing required fields")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: identifiers, status, fraud_type, created_by", startTime)
	}

	// Validate enum values
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be one of: Confirmed, Suspected, Revoked", startTime)
	}

	if !isValidFraudType(req.FraudType) {
		log.Printf("Invalid fraud_type: %s", req.FraudType)
		return createErrorResponse(http.StatusBadRequest, "Invalid fraud_type. Must be one of: Mule, Victim, Fraud", startTime)
	}

	// Initialize blinding service for this company
	log.Printf("Initializing blinding service for company: %s", companyID)
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initialize blinding service", startTime)
	}

	// Process all identifiers and create blinded versions
	var blindedIdentifiers []map[string]string
	var identifierData []IdentifierData
	
	for _, identifier := range req.Identifiers {
		if identifier.ID == "" || identifier.Type == "" {
			log.Println("Invalid identifier: missing id or type")
			return createErrorResponse(http.StatusBadRequest, "Invalid identifier: missing id or type", startTime)
		}

		// Blind the identifier
		log.Printf("Blinding identifier: %s (type: %s)", identifier.ID, identifier.Type)
		blindedElement, err := blindingService.BlindData(identifier.ID)
		if err != nil {
			log.Printf("Failed to blind identifier: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to blind identifier", startTime)
		}

		// Convert blinded element to hex string
		blindedBytes, err := blindedElement.MarshalBinary()
		if err != nil {
			log.Printf("Failed to serialize blinded element: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to serialize blinded element", startTime)
		}
		blindedHex := fmt.Sprintf("%x", blindedBytes)

		// Store blinded identifier for response
		blindedId := map[string]string{
			"id":   blindedHex,
			"type": identifier.Type,
		}
		blindedIdentifiers = append(blindedIdentifiers, blindedId)

		// Add to identifier data for central store request
		identifierData = append(identifierData, IdentifierData{
			ID:   blindedHex,
			Type: identifier.Type,
		})
	}

	// Create single central store request with all identifiers
	// Use the first identifier as the primary one for the fraud data record
	primaryIdentifier := identifierData[0]
	
	centralRequest := CentralStoreRequest{
		BlindedID:      primaryIdentifier.ID,
		IdentifierType: primaryIdentifier.Type,
		Metadata:       req.Metadata,
		InstitutionID:  req.InstitutionID,
		CreatedBy:      req.CreatedBy,
		Status:         req.Status,
		FraudType:      req.FraudType,
		Identifiers:    identifierData, // Include all identifiers for association
	}

	// Send single request to central server
	log.Printf("Sending request to central server with %d identifiers", len(identifierData))
	
	// Variable to store the central server response
	var centralResponse struct {
		Success       bool   `json:"success"`
		Message       string `json:"message"`
		FraudDataID   int    `json:"fraud_data_id,omitempty"`
		AssociationID *int   `json:"association_id,omitempty"`
		CreatedAt     string `json:"created_at,omitempty"`
		UpdatedAt     string `json:"updated_at,omitempty"`
	}
	
	// Serialize request
	requestJSON, err := json.Marshal(centralRequest)
	if err != nil {
		log.Printf("Failed to serialize central request: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize central request", startTime)
	}

	// Get central server URL from environment
	baseAPIURL := os.Getenv("BASE_API_URL")
	if baseAPIURL == "" {
		// Fallback to older env var for backward compatibility
		baseAPIURL = os.Getenv("CENTRAL_SERVER_URL")
		if baseAPIURL == "" {
			log.Printf("BASE_API_URL environment variable not set")
			return createErrorResponse(http.StatusInternalServerError, "API URL not configured", startTime)
		}
	}
	
	// Ensure the URL ends with a slash
	if !strings.HasSuffix(baseAPIURL, "/") {
		baseAPIURL += "/"
	}
	
	centralServerURL := baseAPIURL + "central/store"

	// Send request to central server
	log.Printf("Sending request to central server: %s", centralServerURL)
	resp, err := http.Post(centralServerURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		log.Printf("Failed to send request to central server: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to send request to central server", startTime)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		log.Printf("Central server returned error status: %d", resp.StatusCode)
		return createErrorResponse(http.StatusInternalServerError, "Central server returned error status", startTime)
	}
	
	// Parse the central server response
	if err := json.NewDecoder(resp.Body).Decode(&centralResponse); err != nil {
		log.Printf("Failed to parse central server response: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to parse central server response", startTime)
	}
	
	if !centralResponse.Success {
		log.Printf("Central server returned error: %s", centralResponse.Message)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Central server error: %s", centralResponse.Message), startTime)
	}

	// Create insert ID in the format INS_DATE_{actualId}
	currentDate := time.Now().Format("20060102")
	insertID := fmt.Sprintf("INS-%s-", currentDate)
	
	// If we got a fraud data ID from the central server, append it to the insert ID
	if centralResponse.FraudDataID > 0 {
		insertID += fmt.Sprintf("%d", centralResponse.FraudDataID)
	} else {
		// Generate a random string if we didn't get an ID
		insertID += generateRandomString(6)
	}
	
	// Log association creation if successful
	if centralResponse.AssociationID != nil {
		log.Printf("Successfully created association with ID: %d", *centralResponse.AssociationID)
	}



	// Get institution info from database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database", startTime)
	}
	defer db.Close()
	
	var institution map[string]interface{}
	institutionInfo, err := db.GetInstitutionByID(req.InstitutionID)
	if err != nil {
		log.Printf("Failed to get institution info: %v", err)
		// Use default values if institution lookup fails
		institution = map[string]interface{}{
			"name": "Unknown Institution",
			"id":   req.InstitutionID,
			"type": "Unknown",
		}
	} else {
		institutionType := "Unknown"
		if institutionInfo.InstitutionType != nil {
			institutionType = *institutionInfo.InstitutionType
		}
		
		institution = map[string]interface{}{
			"name": institutionInfo.InstitutionName,
			"id":   req.InstitutionID,
			"type": institutionType,
		}
	}

	// Use timestamps from central store response, fallback to current time if not available
	createdDatetime := centralResponse.CreatedAt
	lastUpdatedDatetime := centralResponse.UpdatedAt
	if createdDatetime == "" {
		now := time.Now()
		createdDatetime = now.Format(time.RFC3339)
		lastUpdatedDatetime = now.Format(time.RFC3339)
	}
	
	// Calculate final response time after central server call
	finalResponseTime := time.Since(startTime)
	finalResponseTimeMs := int(finalResponseTime.Milliseconds())
	
	// Prepare response
	response := SubmitResponse{
		Success:             true,
		Message:             fmt.Sprintf("Successfully submitted %d fraud data records with associations", len(req.Identifiers)),
		InsertID:            insertID,
		AssociationID:       centralResponse.AssociationID,
		Identifiers:         req.Identifiers,
		BlindedIdentifiers:  blindedIdentifiers,
		Institution:         institution,
		Status:              "active",
		FraudType:           req.FraudType,
		FraudStatus:         req.Status,
		CreatedDatetime:     createdDatetime,
		LastUpdatedDatetime: lastUpdatedDatetime,
		ResponseTime:        int64(finalResponseTimeMs),
	}

	// Create audit log entry for data submission
	auditLog := &database.AuditLog{
		EventType:     "fraud_data_submitted",
		InstitutionID: &institutionID,
		UserID:        req.CreatedBy,
		Metadata: map[string]interface{}{
			"identifiers_count": len(req.Identifiers),
			"status":            req.Status,
			"fraud_type":        req.FraudType,
			"insert_id":         insertID,
			"central_fraud_data_id": centralResponse.FraudDataID,
			"response_time_ms":  finalResponseTimeMs,
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
			"X-Response-Time":              fmt.Sprintf("%dms", finalResponseTimeMs),
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string, startTime time.Time) (events.APIGatewayProxyResponse, error) {
	// Calculate response time for error response
	responseTime := time.Since(startTime)
	
	response := SubmitResponse{
		Success:      false,
		Message:      message,
		Status:       "Failed",
		ResponseTime: responseTime.Milliseconds(),
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
			"X-Response-Time":              fmt.Sprintf("%dms", responseTime.Milliseconds()),
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}

// generateRandomString generates a random string of the specified length
func generateRandomString(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"Confirmed": true,
		"Suspected": true,
		"Revoked":   true,
	}
	return validStatuses[status]
}

func isValidFraudType(fraudType string) bool {
	validTypes := map[string]bool{
		"Mule":    true,
		"Victim":  true,
		"Fraud":   true,
	}
	return validTypes[fraudType]
}
