package main

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type EditFraudStatusRequest struct {
	FraudID   int    `json:"fraud_id"`   // ID of the fraud data record to edit
	Status    string `json:"status"`     // New status (Confirmed, Suspected, Absolved)
	FraudType string `json:"fraud_type"` // New fraud type
	UpdatedBy string `json:"updated_by"` // User making the update
}

type EditFraudStatusResponse struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	FraudData *database.FraudData    `json:"fraud_data,omitempty"`
	UpdatedAt string                 `json:"updated_at,omitempty"`
	Status    string                 `json:"status,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-edit-fraud-status")
	
	// Extract institution ID from path parameters
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters")
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id")
	}
	
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req EditFraudStatusRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.FraudID == 0 || req.Status == "" || req.FraudType == "" || req.UpdatedBy == "" {
		log.Println("Missing required fields")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: fraud_id, status, fraud_type, updated_by")
	}

	// Validate enum values
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be one of: Confirmed, Suspected, Revoked")
	}

	if !isValidFraudType(req.FraudType) {
		log.Printf("Invalid fraud_type: %s", req.FraudType)
		return createErrorResponse(http.StatusBadRequest, "Invalid fraud_type. Must be one of: Mule, Victim, Fraud")
	}



	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database")
	}
	defer db.Close()

	// Get the fraud data record to verify it exists and belongs to this institution
	fraudData, err := db.GetFraudDataByID(req.FraudID)
	if err != nil {
		log.Printf("Failed to get fraud data: %v", err)
		return createErrorResponse(http.StatusNotFound, "Fraud data not found")
	}

	// Check if the fraud data belongs to this institution
	if fraudData.InstitutionID != institutionID {
		log.Printf("Fraud data belongs to institution %d, but request is from institution %d", fraudData.InstitutionID, institutionID)
		return createErrorResponse(http.StatusForbidden, "Access denied: fraud data does not belong to this institution")
	}

	// Check if the record is active
	if fraudData.RowStatus != "active" {
		log.Printf("Fraud data record is not active: %s", fraudData.RowStatus)
		return createErrorResponse(http.StatusBadRequest, "Cannot edit inactive fraud data record")
	}

	// Update the fraud data status
	// If the fraud data has an association, update all related records
	if fraudData.AssociationID != nil {
		log.Printf("Fraud data has association ID %d, updating all related records", *fraudData.AssociationID)
		err = db.UpdateFraudDataStatusByAssociationID(*fraudData.AssociationID, req.Status, req.FraudType, req.UpdatedBy)
		if err != nil {
			log.Printf("Failed to update fraud data status by association: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to update fraud data status")
		}
		log.Printf("Successfully updated all fraud data records with association ID %d", *fraudData.AssociationID)
	} else {
		// Update only the single fraud data record
		err = db.UpdateFraudDataStatus(req.FraudID, req.Status, req.FraudType, req.UpdatedBy)
		if err != nil {
			log.Printf("Failed to update fraud data status: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to update fraud data status")
		}
	}

	// Get the updated fraud data
	updatedFraudData, err := db.GetFraudDataByID(req.FraudID)
	if err != nil {
		log.Printf("Failed to get updated fraud data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to retrieve updated fraud data")
	}

	// Create audit log entry
	auditMetadata := map[string]interface{}{
		"fraud_id":    req.FraudID,
		"old_status":  fraudData.Status,
		"new_status":  req.Status,
		"old_fraud_type": fraudData.FraudType,
		"new_fraud_type": req.FraudType,
		"identifier":  fraudData.Identifier,
		"identifier_type": fraudData.IdentifierType,
	}
	
	// Add association information if present
	if fraudData.AssociationID != nil {
		auditMetadata["association_id"] = *fraudData.AssociationID
		auditMetadata["updated_by_association"] = true
	} else {
		auditMetadata["updated_by_association"] = false
	}

	auditLog := &database.AuditLog{
		EventType:     "fraud_status_updated",
		InstitutionID: &institutionID,
		UserID:        req.UpdatedBy,
		Metadata:      auditMetadata,
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	// Prepare response
	response := EditFraudStatusResponse{
		Success:   true,
		Message:   "Fraud status updated successfully",
		FraudData: updatedFraudData,
		UpdatedAt: time.Now().Format("2006-01-02 15:04:05"),
		Status:    "Success",
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "PUT, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}



func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	response := EditFraudStatusResponse{
		Success: false,
		Message: message,
		Status:  "Failed",
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "PUT, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"Confirmed": true,
		"Suspected": true,
		"Revoked":   true,
	}
	return validStatuses[status]
}

func isValidFraudType(fraudType string) bool {
	validTypes := map[string]bool{
		"Mule":    true,
		"Victim":  true,
		"Fraud":   true,
	}
	return validTypes[fraudType]
}

func main() {
	lambda.Start(handler)
} 