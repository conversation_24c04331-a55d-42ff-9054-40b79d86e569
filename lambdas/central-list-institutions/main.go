package main

import (
	"context"
	"encoding/json"
	"net/http"
	"log"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type ListInstitutionsResponse struct {
	Success      bool                    `json:"success"`
	Message      string                  `json:"message"`
	Institutions []*database.Institution `json:"institutions,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: central-list-institutions")
	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	institutions, err := db.ListInstitutions()
	if err != nil {
		log.Printf("Failed to list institutions: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to list institutions")
	}

	// Create audit log entry for institution listing
	auditLog := &database.AuditLog{
		EventType: "institutions_listed",
		UserID:    "system", // Since this is a central operation
		Metadata: map[string]interface{}{
			"institutions_count": len(institutions),
			"operation":          "list_institutions",
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Don't fail the request if audit logging fails
	}

	response := ListInstitutionsResponse{
		Success:      true,
		Message:      "Institutions fetched successfully",
		Institutions: institutions,
	}
	responseJSON, _ := json.Marshal(response)
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	responseJSON, _ := json.Marshal(errorResponse)
	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "GET, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
