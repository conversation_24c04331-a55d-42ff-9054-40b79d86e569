package main

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/cloudflare/circl/group"
	"github.com/google/uuid"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type IdentifierQuery struct {
	Identifier string `json:"identifier"`
	Type       string `json:"type"`
}

type QueryRequest struct {
	Identifiers []IdentifierQuery `json:"identifiers"`
}

type CentralCrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"`
	RequestingCompany string `json:"requesting_company"`
}

type CentralGetDataRequest struct {
	BlindedValues map[string]string `json:"blinded_values"`
}

type QueryResponse struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	FraudData   []FraudDataResult      `json:"fraud_data,omitempty"`
	TaskID      string                 `json:"task_id,omitempty"`
	QueryID     string                 `json:"query_id,omitempty"`
	QueryDate   string                 `json:"query_date,omitempty"`
	Summary     map[string]interface{} `json:"summary,omitempty"`
	Processes   []map[string]interface{} `json:"processes,omitempty"`
	Timeline    []map[string]interface{} `json:"timeline,omitempty"`
	Institution map[string]interface{} `json:"institution,omitempty"`
	Identifiers []map[string]interface{} `json:"identifiers,omitempty"`
	BlindedIdentifiers []map[string]string `json:"blinded_identifiers,omitempty"`
	EncryptionTable []EncryptionTableRow `json:"encryption_table,omitempty"`
	Stats       map[string]interface{} `json:"stats,omitempty"`
	Status      string                 `json:"status,omitempty"`
	ResponseTime int64                 `json:"response_time_ms,omitempty"`
}

type FraudDataResult struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	InstitutionName string                `json:"institution_name,omitempty"`
	InstitutionType string                `json:"institution_type,omitempty"`
	CreatedBy      string                 `json:"created_by"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	CreatedAt      string                 `json:"created_at"`
	ReportID       string                 `json:"report_id,omitempty"`
	IncidentID     string                 `json:"incident_id,omitempty"`
	AssociationID  *int                   `json:"association_id,omitempty"`
}

type EncryptionTableRow struct {
	RowID                 int    `json:"row_id"`
	QueryID               string `json:"query_id"`
	OriginalIdentifier    string `json:"original_identifier"`
	IdentifierType        string `json:"identifier_type"`
	EncryptedQuery        string `json:"encrypted_query"`
	Intermediate          string `json:"intermediate"`
	EncryptedByPartner    string `json:"encrypted_by_partner"`
	Consortium            string `json:"consortium"`
	CompanyID             string `json:"company_id,omitempty"`
	PartnerCompanyID      string `json:"partner_company_id,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	startTime := time.Now()
	log.Println("Handler started: company-query")
	
	institutionIDStr, ok := request.PathParameters["institution_id"]
	if !ok {
		log.Println("Missing institution_id in path parameters")
		return createErrorResponse(http.StatusBadRequest, "Missing institution_id in path parameters", startTime)
	}
	
	institutionID, err := strconv.Atoi(institutionIDStr)
	if err != nil {
		log.Printf("Invalid institution_id: %s", institutionIDStr)
		return createErrorResponse(http.StatusBadRequest, "Invalid institution_id", startTime)
	}
	
	companyID := fmt.Sprintf("company_%d", institutionID)
	
	log.Printf("Request body: %s", request.Body)
	var req QueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body", startTime)
	}
	
	log.Printf("Parsed request: %+v", req)
	if len(req.Identifiers) == 0 {
		log.Println("Missing required field: identifiers")
		return createErrorResponse(http.StatusBadRequest, "Missing required field: identifiers", startTime)
	}

	for i, identifier := range req.Identifiers {
		if identifier.Identifier == "" {
			log.Printf("Missing identifier at index %d", i)
			return createErrorResponse(http.StatusBadRequest, fmt.Sprintf("Missing identifier at index %d", i), startTime)
		}
		if identifier.Type == "" {
			log.Printf("Missing type for identifier at index %d", i)
			return createErrorResponse(http.StatusBadRequest, fmt.Sprintf("Missing type for identifier at index %d", i), startTime)
		}
	}

	log.Printf("Initializing blinding service for company: %s", companyID)
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to initialize blinding service", startTime)
	}

	db, err := database.New()
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to connect to database", startTime)
	}
	defer db.Close()

	taskID := uuid.New()
	now := time.Now()
	
	steps := []map[string]interface{}{
		{
			"step":        "secure_query_creation",
			"description": "Query securely created and signed by institution",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "completed",
		},
		{
			"step":        "encrypted_partner_communication",
			"description": "Query encrypted and sent to partner institutions",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
		{
			"step":        "encrypted_consortium_lookup",
			"description": "Consortium-wide lookup and validation",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
		{
			"step":        "report_generation",
			"description": "Final report generated and delivered",
			"timestamp":   now.Format(time.RFC3339),
			"status":      "pending",
		},
	}
	
	taskLog := &database.TaskLog{
		TaskID:             taskID,
		TaskType:           "query",
		BlindedIdentifiers: []database.BlindedIdentifier{},
		InstitutionID:      &institutionID,
		Status:             "processing",
		CreatedBy:          institutionIDStr,
		StartTime:          &now,
		Steps:              steps,
	}

	if err := db.CreateTaskLog(taskLog); err != nil {
		log.Printf("Failed to create task log: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to create task log", startTime)
	}

	var allFraudData []FraudDataResult
	var allBlindedIdentifiers []map[string]string
	var allDoubleEncryptedValues map[string]string = make(map[string]string)
	var allSingleDecryptedValues map[string]string = make(map[string]string)
	
	updateTaskLogStep(db, taskID, "secure_query_creation", "completed", "Query securely created and signed by institution")
	
	for _, identifierQuery := range req.Identifiers {
		log.Printf("Processing identifier: %s of type: %s", identifierQuery.Identifier, identifierQuery.Type)
		
		blindedElement, err := blindingService.BlindData(identifierQuery.Identifier)
		if err != nil {
			log.Printf("Failed to blind identifier %s: %v", identifierQuery.Identifier, err)
			continue
		}

		blindedBytes, err := blindedElement.MarshalBinary()
		if err != nil {
			log.Printf("Failed to serialize blinded element for %s: %v", identifierQuery.Identifier, err)
			continue
		}
		blindedHex := fmt.Sprintf("%x", blindedBytes)
		
		allBlindedIdentifiers = append(allBlindedIdentifiers, map[string]string{
			"id":   blindedHex,
			"type": identifierQuery.Type,
		})
		
		fraudData, doubleEncryptedValues, singleDecryptedValues := processSingleIdentifier(blindingService, companyID, blindedHex, identifierQuery.Type, db, taskID, startTime)
		allFraudData = append(allFraudData, fraudData...)
		
		for k, v := range doubleEncryptedValues {
			allDoubleEncryptedValues[k] = v
		}
		for k, v := range singleDecryptedValues {
			allSingleDecryptedValues[k] = v
		}
	}
	
	updateTaskLogStep(db, taskID, "encrypted_partner_communication", "completed", "Query encrypted and sent to partner institutions")
	updateTaskLogStep(db, taskID, "encrypted_consortium_lookup", "completed", "Consortium-wide lookup and validation")
	
	if len(allBlindedIdentifiers) == 0 {
		return createErrorResponse(http.StatusInternalServerError, "Failed to process any identifiers", startTime)
	}
	
	taskLogUpdate, err := db.GetTaskLogByTaskID(taskID)
	if err == nil {
		var dbBlindedIdentifiers []database.BlindedIdentifier
		for _, blindedId := range allBlindedIdentifiers {
			dbBlindedIdentifiers = append(dbBlindedIdentifiers, database.BlindedIdentifier{
				Identifier: blindedId["id"],
				Type:       blindedId["type"],
			})
		}
		taskLogUpdate.BlindedIdentifiers = dbBlindedIdentifiers
		db.UpdateTaskLog(taskLogUpdate)
	}

	responseTime := time.Since(startTime)
	
	completeTaskLog(db, taskID, allFraudData, "Query completed successfully", responseTime, allBlindedIdentifiers, allDoubleEncryptedValues, allSingleDecryptedValues, req.Identifiers, institutionID)
	
	saveQueryResponseDetails(db, taskID, institutionID, req.Identifiers, allBlindedIdentifiers, allDoubleEncryptedValues, allSingleDecryptedValues, allFraudData, responseTime)
	
	return createSuccessResponseWithMultipleIdentifiers(allFraudData, "Query completed successfully", taskID.String(), allBlindedIdentifiers, allDoubleEncryptedValues, allSingleDecryptedValues, req.Identifiers, responseTime, institutionID)
}

func processSingleIdentifier(blindingService *company.BlindingService, companyID, blindedHex, identifierType string, db *database.DB, taskID uuid.UUID, startTime time.Time) ([]FraudDataResult, map[string]string, map[string]string) {
	crossQueryRequest := CentralCrossQueryRequest{
		BlindedIdentifier: blindedHex,
		RequestingCompany: companyID,
	}

	log.Printf("Sending cross-query request to central server for identifier type: %s", identifierType)
	crossQueryJSON, err := json.Marshal(crossQueryRequest)
	if err != nil {
		log.Printf("Failed to serialize cross-query request: %v", err)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}

	baseAPIURL := os.Getenv("BASE_API_URL")
	if baseAPIURL == "" {
		baseAPIURL = os.Getenv("CENTRAL_API_URL")
		if baseAPIURL == "" {
			baseAPIURL = os.Getenv("CENTRAL_SERVER_URL")
			if baseAPIURL == "" {
				log.Printf("BASE_API_URL environment variable not set")
				return []FraudDataResult{}, make(map[string]string), make(map[string]string)
			}
		}
	}
	
	if !strings.HasSuffix(baseAPIURL, "/") {
		baseAPIURL += "/"
	}
	
	centralServerURL := baseAPIURL

	crossQueryResp, err := http.Post(centralServerURL+"central/cross-query", "application/json", bytes.NewBuffer(crossQueryJSON))
	if err != nil {
		log.Printf("Failed to send cross-query request: %v", err)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}
	defer crossQueryResp.Body.Close()

	if crossQueryResp.StatusCode != http.StatusOK {
		log.Printf("Central server returned error status for cross-query: %d", crossQueryResp.StatusCode)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}

	var crossQueryResult struct {
		Success       bool                `json:"success"`
		Message       string              `json:"message"`
		BlindedValues map[string]string   `json:"blinded_values,omitempty"`
	}
	
	if err := json.NewDecoder(crossQueryResp.Body).Decode(&crossQueryResult); err != nil {
		log.Printf("Failed to parse cross-query response: %v", err)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}

	if !crossQueryResult.Success || len(crossQueryResult.BlindedValues) == 0 {
		log.Printf("Cross-query unsuccessful or no results for identifier type: %s", identifierType)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}

	log.Printf("Decrypting blinded values from other companies for identifier type: %s", identifierType)
	otherCompanyBlindedValues := make(map[string]group.Element)
	for otherCompanyID, blindedValueHex := range crossQueryResult.BlindedValues {
		if otherCompanyID == companyID {
			continue
		}

		blindedValueBytes, err := hex.DecodeString(blindedValueHex)
		if err != nil {
			log.Printf("Failed to decode blinded value from %s: %v", otherCompanyID, err)
			continue
		}

		element := blindingService.GetGroup().NewElement()
		if err := element.UnmarshalBinary(blindedValueBytes); err != nil {
			log.Printf("Failed to unmarshal blinded value from %s: %v", otherCompanyID, err)
			continue
		}

		otherCompanyBlindedValues[otherCompanyID] = element
	}

	decryptedValues, err := blindingService.DecryptDoubleEncryptedValues(otherCompanyBlindedValues)
	if err != nil {
		log.Printf("Failed to decrypt values: %v", err)
		return []FraudDataResult{}, make(map[string]string), make(map[string]string)
	}

	getDataRequest := CentralGetDataRequest{
		BlindedValues: decryptedValues,
	}

	getDataRequest.BlindedValues[companyID] = blindedHex

	log.Printf("Sending get-data request to central server for identifier type: %s", identifierType)
	getDataJSON, err := json.Marshal(getDataRequest)
	if err != nil {
		log.Printf("Failed to serialize get-data request: %v", err)
		return []FraudDataResult{}, crossQueryResult.BlindedValues, decryptedValues
	}

	getDataResp, err := http.Post(centralServerURL+"central/get-data", "application/json", bytes.NewBuffer(getDataJSON))
	if err != nil {
		log.Printf("Failed to send get-data request: %v", err)
		return []FraudDataResult{}, crossQueryResult.BlindedValues, decryptedValues
	}
	defer getDataResp.Body.Close()

	if getDataResp.StatusCode != http.StatusOK {
		log.Printf("Central server returned error status for get-data: %d", getDataResp.StatusCode)
		return []FraudDataResult{}, crossQueryResult.BlindedValues, decryptedValues
	}

	var getDataResult struct {
		Success   bool              `json:"success"`
		Message   string            `json:"message"`
		FraudData []FraudDataResult `json:"fraud_data,omitempty"`
	}
	
	if err := json.NewDecoder(getDataResp.Body).Decode(&getDataResult); err != nil {
		log.Printf("Failed to parse get-data response: %v", err)
		return []FraudDataResult{}, crossQueryResult.BlindedValues, decryptedValues
	}

	if !getDataResult.Success {
		log.Printf("Get-data unsuccessful for identifier type %s: %s", identifierType, getDataResult.Message)
		return []FraudDataResult{}, crossQueryResult.BlindedValues, decryptedValues
	}

	for i := range getDataResult.FraudData {
		if getDataResult.FraudData[i].IdentifierType == "" {
			getDataResult.FraudData[i].IdentifierType = identifierType
		}
	}

	return getDataResult.FraudData, crossQueryResult.BlindedValues, decryptedValues
}

func prepareQueryResponse(fraudData []FraudDataResult, message string, taskID string, blindedIdentifiers []map[string]string, doubleEncryptedValues map[string]string, singleDecryptedValues map[string]string, originalIdentifiers []IdentifierQuery, queryingInstitutionID int) QueryResponse {
	queryID := taskID
	
	totalMatches := len(fraudData)
	confirmedCount := 0
	suspectedCount := 0
	revokedCount := 0
	
	identifiersByType := make(map[string][]map[string]interface{})
	timeline := []map[string]interface{}{}
	
	for _, data := range fraudData {
		switch data.Status {
		case "Confirmed":
			confirmedCount++
		case "Suspected":
			suspectedCount++
		case "Absolved":
			revokedCount++
		}
		
		if _, exists := identifiersByType[data.IdentifierType]; !exists {
			identifiersByType[data.IdentifierType] = []map[string]interface{}{}
		}
		
		identifiersByType[data.IdentifierType] = append(identifiersByType[data.IdentifierType], map[string]interface{}{
			"id": data.Identifier,
			"type": data.IdentifierType,
			"status": data.Status,
		})
		
		t, _ := time.Parse(time.RFC3339, data.CreatedAt)
		dateStr := t.Format("2006/01/02")
		timeStr := t.Format("15:04:05")
		
		institutionName, institutionType := getInstitutionInfo(data.InstitutionID)
		
		data.InstitutionName = institutionName
		if institutionType != "" {
			data.InstitutionType = institutionType
		}
		
		eventType := data.FraudType
		
		incidentID := data.IncidentID
		if incidentID == "" {
			if data.AssociationID != nil {
				incidentID = fmt.Sprintf("INS-%s-%d", t.Format("20060102"), *data.AssociationID)
			} else {
				incidentID = fmt.Sprintf("INS-%s-%d", t.Format("20060102"), data.ID)
			}
		}
		
		timelineEntry := map[string]interface{}{
			"date": dateStr,
			"time": timeStr,
			"institution": institutionName,
			"institution_type": institutionType,
			"event_id": incidentID,
			"identifier": data.Identifier,
			"identifier_type": data.IdentifierType,
			"status": data.Status,
			"event_type": eventType,
		}
		
		timeline = append(timeline, timelineEntry)
	}
	
	var identifiers []map[string]interface{}
	for idType, ids := range identifiersByType {
		identifiers = append(identifiers, map[string]interface{}{
			"type": idType,
			"values": ids,
			"total": len(ids),
			"past_1y": 0,
			"past_1m": 0,
		})
	}
	
	processes := createDefaultProcesses()
	
	institutionName, institutionType := getInstitutionInfo(queryingInstitutionID)
	
	for i := range fraudData {
		fraudInstitutionName, fraudInstitutionType := getInstitutionInfo(fraudData[i].InstitutionID)
		fraudData[i].InstitutionName = fraudInstitutionName
		if fraudInstitutionType != "" {
			fraudData[i].InstitutionType = fraudInstitutionType
		}
	}
	
	currentDate := time.Now().Format("20060102")
	summary := map[string]interface{}{
		"institution": map[string]interface{}{
			"name": institutionName,
			"type": institutionType,
		},
		"status": "Success",
		"date": time.Now().Format("02/01/2006"),
		"report_id": fmt.Sprintf("RPT-%s-%s", currentDate, strings.ReplaceAll(institutionName, " ", "")),
		"identifiers": len(identifiersByType),
	}
	
	firstMatchDate, latestMatchDate := calculateMatchDateRange(fraudData)
	
	stats := map[string]interface{}{
		"total_matches": totalMatches,
		"confirmed": confirmedCount,
		"suspected": suspectedCount,
		"revoked": revokedCount,
		"first_match": firstMatchDate,
		"latest_match": latestMatchDate,
	}

	encryptionTable := buildEncryptionTable(taskID, blindedIdentifiers, doubleEncryptedValues, singleDecryptedValues, originalIdentifiers)

	return QueryResponse{
		Success:     true,
		Message:     message,
		FraudData:   fraudData,
		TaskID:      taskID,
		QueryID:     queryID,
		QueryDate:   time.Now().Format("02/01/2006 15:04:05"),
		Summary:     summary,
		Timeline:    timeline,
		Processes:   processes,
		Institution: map[string]interface{}{
			"name": institutionName,
			"type": institutionType,
		},
		Identifiers: identifiers,
		BlindedIdentifiers: blindedIdentifiers,
		EncryptionTable: encryptionTable,
		Stats:       stats,
		Status:      "Success",
	}
}

func createDefaultProcesses() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id": 1,
			"name": "Secure Query Creation",
			"description": "Query securely created and signed by institution",
			"status": "Success",
			"timestamp": time.Now().Add(-3 * time.Minute).Format("2006/01/02 15:04"),
		},
		{
			"id": 2,
			"name": "Encrypted Partner Communication",
			"description": "Query encrypted and sent to partner institutions",
			"status": "Success",
			"timestamp": time.Now().Add(-2 * time.Minute).Format("2006/01/02 15:04"),
		},
		{
			"id": 3,
			"name": "Encrypted Consortium Lookup",
			"description": "Consortium-wide lookup and validation",
			"status": "Success",
			"timestamp": time.Now().Add(-1 * time.Minute).Format("2006/01/02 15:04"),
		},
		{
			"id": 4,
			"name": "Report Generation",
			"description": "Final report generated and delivered",
			"status": "Success",
			"timestamp": time.Now().Format("2006/01/02 15:04"),
		},
	}
}

func calculateMatchDateRange(fraudData []FraudDataResult) (string, string) {
	if len(fraudData) == 0 {
		return time.Now().Format("02/06/2006"), time.Now().Format("02/06/2006")
	}
	
	firstMatchDate := fraudData[0].CreatedAt
	latestMatchDate := fraudData[0].CreatedAt
	
	for _, data := range fraudData {
		currentDate, err := time.Parse(time.RFC3339, data.CreatedAt)
		if err != nil {
			continue
		}
		
		firstDate, err := time.Parse(time.RFC3339, firstMatchDate)
		if err != nil {
			firstMatchDate = data.CreatedAt
			continue
		}
		
		lastDate, err := time.Parse(time.RFC3339, latestMatchDate)
		if err != nil {
			latestMatchDate = data.CreatedAt
			continue
		}
		
		if currentDate.Before(firstDate) {
			firstMatchDate = data.CreatedAt
		}
		
		if currentDate.After(lastDate) {
			latestMatchDate = data.CreatedAt
		}
	}
	
	if t, err := time.Parse(time.RFC3339, firstMatchDate); err == nil {
		firstMatchDate = t.Format("02/06/2006")
	}
	
	if t, err := time.Parse(time.RFC3339, latestMatchDate); err == nil {
		latestMatchDate = t.Format("02/06/2006")
	}
	
	return firstMatchDate, latestMatchDate
}

func updateResponseWithTaskLogSteps(response QueryResponse, taskLog *database.TaskLog) QueryResponse {
	if taskLog == nil {
		return response
	}
	
	finalSteps := filterCompletedSteps(taskLog.Steps)
	
	workflowSteps := []struct {
		ID          int
		Name        string
		Description string
	}{
		{1, "secure_query_creation", "Secure Query Creation"},
		{2, "encrypted_partner_communication", "Encrypted Partner Communication"},
		{3, "encrypted_consortium_lookup", "Encrypted Consortium Lookup"},
		{4, "report_generation", "Report Generation"},
	}
	
	stepMap := make(map[string]map[string]interface{})
	
	for _, step := range workflowSteps {
		stepMap[step.Name] = map[string]interface{}{
			"id":          step.ID,
			"name":        step.Description,
			"description": getStepDescription(step.Name),
			"status":      "Pending",
		}
	}
	
	for _, step := range finalSteps {
		stepName, ok := step["step"].(string)
		if !ok {
			continue
		}
		
		if _, exists := stepMap[stepName]; !exists {
			continue
		}
		
		status, _ := step["status"].(string)
		timestamp, _ := step["timestamp"].(string)
		
		uiStatus := "Pending"
		if status == "completed" {
			uiStatus = "Success"
		} else if status == "failed" {
			uiStatus = "Failed"
		} else if status == "in_progress" {
			uiStatus = "In Progress"
		}
		
		stepMap[stepName]["status"] = uiStatus
		
		if timestamp != "" {
			t, err := time.Parse(time.RFC3339, timestamp)
			if err == nil {
				stepMap[stepName]["timestamp"] = t.Format("2006/01/02 15:04")
			} else {
				stepMap[stepName]["timestamp"] = timestamp
			}
		}
	}
	
	var processes []map[string]interface{}
	for _, step := range workflowSteps {
		stepInfo := stepMap[step.Name]
		processes = append(processes, stepInfo)
	}
	
	response.Processes = processes
	
	return response
}

func completeTaskLog(db *database.DB, taskID uuid.UUID, fraudData []FraudDataResult, message string, responseTime time.Duration, blindedIdentifiers []map[string]string, doubleEncryptedValues map[string]string, singleDecryptedValues map[string]string, originalIdentifiers []IdentifierQuery, queryingInstitutionID int) {
	updateTaskLogStep(db, taskID, "report_generation", "completed", "Final report generated and delivered")
	
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		log.Printf("Failed to get task log: %v", err)
		return
	}
	
	taskLog.Status = "completed"
	now := time.Now()
	taskLog.UpdatedAt = now
	taskLog.QueryCompletedAt = &now
	taskLog.EndTime = &now
	
	response := prepareQueryResponse(fraudData, message, taskID.String(), blindedIdentifiers, doubleEncryptedValues, singleDecryptedValues, originalIdentifiers, queryingInstitutionID)
	response.ResponseTime = responseTime.Milliseconds()
	
	response = updateResponseWithTaskLogSteps(response, taskLog)
	
	responseMap := make(map[string]interface{})
	responseJSON, _ := json.Marshal(response)
	json.Unmarshal(responseJSON, &responseMap)
	
	taskLog.Response = responseMap
	taskLog.Steps = filterCompletedSteps(taskLog.Steps)
	
	if err := db.UpdateTaskLog(taskLog); err != nil {
		log.Printf("Failed to update task log with full response: %v", err)
	} else {
		log.Printf("Successfully saved full response to task_log for task ID: %s", taskID)
	}
}

func filterCompletedSteps(steps []map[string]interface{}) []map[string]interface{} {
	stepMap := make(map[string]map[string]interface{})
	
	for _, step := range steps {
		stepName, ok := step["step"].(string)
		if !ok {
			continue
		}
		
		stepMap[stepName] = step
	}
	
	finalSteps := make([]map[string]interface{}, 0, len(stepMap))
	for _, step := range stepMap {
		finalSteps = append(finalSteps, step)
	}
	
	return finalSteps
}

func updateTaskLogStep(db *database.DB, taskID uuid.UUID, stepName, status, description string) {
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		log.Printf("Failed to get task log for step update: %v", err)
		return
	}
	
	now := time.Now()
	updatedStep := map[string]interface{}{
		"step":        stepName,
		"description": description,
		"timestamp":   now.Format(time.RFC3339),
		"status":      status,
	}
	
	taskLog.Steps = updateStepInTaskLog(taskLog.Steps, updatedStep)
	
	if err := db.UpdateTaskLog(taskLog); err != nil {
		log.Printf("Failed to update task log step: %v", err)
	}
}

func updateStepInTaskLog(steps []map[string]interface{}, newStep map[string]interface{}) []map[string]interface{} {
	stepName, ok := newStep["step"].(string)
	if !ok {
		return append(steps, newStep)
	}
	
	for i, step := range steps {
		existingStepName, ok := step["step"].(string)
		if ok && existingStepName == stepName {
			steps[i] = newStep
			return steps
		}
	}
	
	return append(steps, newStep)
}

func createSuccessResponseWithMultipleIdentifiers(fraudData []FraudDataResult, message string, taskID string, blindedIdentifiers []map[string]string, doubleEncryptedValues map[string]string, singleDecryptedValues map[string]string, originalIdentifiers []IdentifierQuery, responseTime time.Duration, queryingInstitutionID int) (events.APIGatewayProxyResponse, error) {
	response := prepareQueryResponse(fraudData, message, taskID, blindedIdentifiers, doubleEncryptedValues, singleDecryptedValues, originalIdentifiers, queryingInstitutionID)
	response.ResponseTime = responseTime.Milliseconds()
	
	if taskID != "" {
		taskUUID, err := uuid.Parse(taskID)
		if err == nil {
			db, err := database.New()
			if err == nil {
				defer db.Close()
				
				taskLog, err := db.GetTaskLogByTaskID(taskUUID)
				if err == nil {
					response = updateResponseWithTaskLogSteps(response, taskLog)
				}
			}
		}
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
			"X-Response-Time":              fmt.Sprintf("%dms", responseTime.Milliseconds()),
		},
		Body: string(responseJSON),
	}, nil
}

func getStepDescription(stepName string) string {
	switch stepName {
	case "secure_query_creation":
		return "Query securely created and signed by institution"
	case "encrypted_partner_communication":
		return "Query encrypted and sent to partner institutions"
	case "encrypted_consortium_lookup":
		return "Consortium-wide lookup and validation"
	case "report_generation":
		return "Final report generated and delivered"
	default:
		return "Processing step"
	}
}

func createErrorResponse(statusCode int, message string, startTime time.Time) (events.APIGatewayProxyResponse, error) {
	responseTime := time.Since(startTime)
	
	currentDate := time.Now().Format("20060102")
	queryID := fmt.Sprintf("QRY-%s-ERR%03d", currentDate, statusCode)
	
	response := QueryResponse{
		Success:      false,
		Message:      message,
		Status:       "Failed",
		QueryID:      queryID,
		QueryDate:    time.Now().Format("02/01/2006 15:04:05"),
		ResponseTime: responseTime.Milliseconds(),
		Summary: map[string]interface{}{
			"status": "Failed",
			"error": message,
		},
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
			"X-Response-Time":              fmt.Sprintf("%dms", responseTime.Milliseconds()),
		},
		Body: string(responseJSON),
	}, nil
}

func buildEncryptionTable(taskID string, blindedIdentifiers []map[string]string, doubleEncryptedValues map[string]string, singleDecryptedValues map[string]string, originalIdentifiers []IdentifierQuery) []EncryptionTableRow {
	var table []EncryptionTableRow
	rowID := 1
	
	requestingCompanyID := ""
	if len(doubleEncryptedValues) > 0 {
		for companyID := range doubleEncryptedValues {
			requestingCompanyID = companyID
			break
		}
	}
	
	for i, blindedId := range blindedIdentifiers {
		identifier := blindedId["id"]
		identifierType := blindedId["type"]
		
		originalIdentifier := ""
		if i < len(originalIdentifiers) {
			originalIdentifier = originalIdentifiers[i].Identifier
		} else {
			originalIdentifier = fmt.Sprintf("identifier_%d", i)
		}
		
		row := EncryptionTableRow{
			RowID:              rowID,
			QueryID:            taskID,
			OriginalIdentifier: originalIdentifier,
			IdentifierType:     identifierType,
			EncryptedQuery:     identifier,
			CompanyID:          requestingCompanyID,
		}
		
		for companyID, doubleEncryptedValue := range doubleEncryptedValues {
			if companyID == requestingCompanyID {
				continue
			}
			
			partnerRow := row
			partnerRow.RowID = rowID
			partnerRow.QueryID = taskID
			partnerRow.Intermediate = doubleEncryptedValue
			partnerRow.EncryptedByPartner = getSingleDecryptedValue(singleDecryptedValues, companyID)
			partnerRow.Consortium = partnerRow.EncryptedByPartner
			partnerRow.PartnerCompanyID = companyID
			
			table = append(table, partnerRow)
			rowID++
		}
		
		if len(doubleEncryptedValues) == 1 {
			row.Consortium = row.EncryptedQuery
			table = append(table, row)
			rowID++
		}
	}
	
	return table
}

func getSingleDecryptedValue(data map[string]string, companyID string) string {
	if value, exists := data[companyID]; exists {
		return value
	}
	return ""
}

func getInstitutionInfo(institutionID int) (string, string) {
	institutionName := "Unknown Institution"
	var institutionType string
	
	db, err := database.New()
	if err == nil {
		defer db.Close()
		institution, err := db.GetInstitutionByID(institutionID)
		if err == nil {
			if institution.InstitutionName != "" {
				institutionName = institution.InstitutionName
			}
			if institution.InstitutionType != nil {
				institutionType = *institution.InstitutionType
			}
		}
	}
	
	return institutionName, institutionType
}

func saveQueryResponseDetails(db *database.DB, taskID uuid.UUID, institutionID int, originalIdentifiers []IdentifierQuery, blindedIdentifiers []map[string]string, doubleEncryptedValues map[string]string, singleDecryptedValues map[string]string, fraudData []FraudDataResult, responseTime time.Duration) {
	queryID := taskID.String()
	
	var originalIdentifiersData []map[string]interface{}
	for _, identifier := range originalIdentifiers {
		originalIdentifiersData = append(originalIdentifiersData, map[string]interface{}{
			"identifier": identifier.Identifier,
			"type":       identifier.Type,
		})
	}
	
	blindedIdentifiersData := make(map[string]interface{})
	for i, blindedId := range blindedIdentifiers {
		blindedIdentifiersData[fmt.Sprintf("identifier_%d", i)] = blindedId
	}
	
	doubleEncryptedValuesData := make(map[string]interface{})
	for companyID, encryptedValue := range doubleEncryptedValues {
		doubleEncryptedValuesData[companyID] = encryptedValue
	}
	
	singleDecryptedValuesData := make(map[string]interface{})
	for companyID, decryptedValue := range singleDecryptedValues {
		singleDecryptedValuesData[companyID] = decryptedValue
	}
	
	var fraudDataResults []map[string]interface{}
	for _, fraud := range fraudData {
		fraudDataResults = append(fraudDataResults, map[string]interface{}{
			"id":               fraud.ID,
			"identifier":       fraud.Identifier,
			"identifier_type":  fraud.IdentifierType,
			"metadata":         fraud.Metadata,
			"institution_id":   fraud.InstitutionID,
			"institution_name": fraud.InstitutionName,
			"institution_type": fraud.InstitutionType,
			"created_by":       fraud.CreatedBy,
			"status":           fraud.Status,
			"fraud_type":       fraud.FraudType,
			"created_at":       fraud.CreatedAt,
			"report_id":        fraud.ReportID,
			"incident_id":      fraud.IncidentID,
		})
	}
	
	responseSummary := map[string]interface{}{
		"total_identifiers": len(originalIdentifiers),
		"total_matches":     len(fraudData),
		"query_date":        time.Now().Format("2006-01-02 15:04:05"),
		"status":            "completed",
	}
	
	var responseTimeline []map[string]interface{}
	for _, fraud := range fraudData {
		t, _ := time.Parse(time.RFC3339, fraud.CreatedAt)
		responseTimeline = append(responseTimeline, map[string]interface{}{
			"date":             t.Format("2006/01/02"),
			"time":             t.Format("15:04"),
			"institution":      fraud.InstitutionName,
			"institution_type": fraud.InstitutionType,
			"event_id":         fraud.IncidentID,
			"identifier":       fraud.Identifier,
			"identifier_type":  fraud.IdentifierType,
			"status":           fraud.Status,
			"event_type":       fraud.FraudType,
		})
	}
	
	responseProcesses := createDefaultProcesses()
	
	confirmedCount := 0
	suspectedCount := 0
	revokedCount := 0
	
	for _, fraud := range fraudData {
		switch fraud.Status {
		case "Confirmed":
			confirmedCount++
		case "Suspected":
			suspectedCount++
		case "Absolved":
			revokedCount++
		}
	}
	
	responseStats := map[string]interface{}{
		"total_matches": len(fraudData),
		"confirmed":     confirmedCount,
		"suspected":     suspectedCount,
		"revoked":       revokedCount,
		"first_match":   time.Now().Format("02/06/2006"),
		"latest_match":  time.Now().Format("02/06/2006"),
	}
	
	queryResponseDetails := &database.QueryResponseDetails{
		TaskID:                taskID,
		QueryID:               queryID,
		InstitutionID:         institutionID,
		OriginalIdentifiers:   map[string]interface{}{"identifiers": originalIdentifiersData},
		BlindedIdentifiers:    blindedIdentifiersData,
		DoubleEncryptedValues: doubleEncryptedValuesData,
		SingleDecryptedValues: singleDecryptedValuesData,
		FraudDataResults:      map[string]interface{}{"results": fraudDataResults},
		ResponseSummary:       responseSummary,
		ResponseTimeline:      map[string]interface{}{"timeline": responseTimeline},
		ResponseProcesses:     map[string]interface{}{"processes": responseProcesses},
		ResponseStats:         responseStats,
		ResponseTimeMs:        int(responseTime.Milliseconds()),
		Status:                "completed",
	}
	
	if err := db.CreateQueryResponseDetails(queryResponseDetails); err != nil {
		log.Printf("Failed to save query response details: %v", err)
	} else {
		log.Printf("Successfully saved query response details for task ID: %s", taskID)
	}
}

func main() {
	lambda.Start(handler)
}
