package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
	"log"
)

type StoreRequest struct {
	BlindedID        string                 `json:"blinded_id"`
	IdentifierType   string                 `json:"identifier_type"`
	Metadata         map[string]interface{} `json:"metadata"`
	InstitutionID    int                    `json:"institution_id"`
	CreatedBy        string                 `json:"created_by"`
	Status           string                 `json:"status"`
	FraudType        string                 `json:"fraud_type"`
	Identifiers      []IdentifierData       `json:"identifiers,omitempty"`
}

type IdentifierData struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type StoreResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	FraudDataID   int    `json:"fraud_data_id,omitempty"`
	AssociationID *int   `json:"association_id,omitempty"`
	CreatedAt     string `json:"created_at,omitempty"`
	UpdatedAt     string `json:"updated_at,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	startTime := time.Now()
	log.Println("Handler started: central-store")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req StoreRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)

	// Validate required fields
	if req.BlindedID == "" || req.IdentifierType == "" || req.InstitutionID == 0 || 
	   req.CreatedBy == "" || req.Status == "" || req.FraudType == "" {
		log.Printf("Missing required fields in request")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields")
	}

	// Validate enum values
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be one of: Confirmed, Suspected, Revoked")
	}

	if !isValidFraudType(req.FraudType) {
		log.Printf("Invalid fraud_type: %s", req.FraudType)
		return createErrorResponse(http.StatusBadRequest, "Invalid fraud_type. Must be one of: Mule, Victim, Fraud")
	}
	log.Println("Connecting to database...")
	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	var associationID *int
	var fraudDataIDs []int

	// Create fraud data records for all identifiers with deduplication
	allIdentifiers := []IdentifierData{{ID: req.BlindedID, Type: req.IdentifierType}}
	
	// Add additional identifiers, avoiding duplicates
	if len(req.Identifiers) > 0 {
		// Create a map to track existing identifiers (ID + Type combination)
		existingIdentifiers := make(map[string]bool)
		existingIdentifiers[req.BlindedID+":"+req.IdentifierType] = true
		
		for _, identifier := range req.Identifiers {
			key := identifier.ID + ":" + identifier.Type
			if !existingIdentifiers[key] {
				allIdentifiers = append(allIdentifiers, identifier)
				existingIdentifiers[key] = true
			} else {
				log.Printf("Skipping duplicate identifier: %s (type: %s)", identifier.ID, identifier.Type)
			}
		}
	}

	log.Printf("Total unique identifiers to process: %d", len(allIdentifiers))
	log.Printf("Primary blinded ID: %s", req.BlindedID)
	log.Printf("Additional identifiers count: %d", len(req.Identifiers))
	for i, id := range req.Identifiers {
		log.Printf("Additional identifier %d: %s (type: %s)", i+1, id.ID, id.Type)
	}

	// Create association if multiple identifiers provided (more than 1 total)
	if len(allIdentifiers) > 1 {
		log.Printf("Creating ID association for multiple identifiers: %+v", allIdentifiers)
		// Create associations with all identifiers as direct array
		idAssoc := &database.IDAssociation{
			Associations: allIdentifiers,
		}

		if err := db.CreateIDAssociation(idAssoc); err != nil {
			log.Printf("Failed to create ID association: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to create ID association")
		}

		associationID = &idAssoc.ID
		log.Printf("Created association with ID: %d", *associationID)
	}

	log.Printf("Creating fraud data records for %d identifiers", len(allIdentifiers))
	for _, identifier := range allIdentifiers {
		log.Printf("Creating fraud data for identifier: %s (type: %s)", identifier.ID, identifier.Type)
		
		// Calculate response time for this record
		responseTime := time.Since(startTime)
		responseTimeMs := int(responseTime.Milliseconds())
		
		fraudData := &database.FraudData{
			Identifier:     identifier.ID,
			IdentifierType: identifier.Type,
			Metadata:       req.Metadata,
			InstitutionID:  req.InstitutionID,
			CreatedBy:      req.CreatedBy,
			Status:         req.Status,
			FraudType:      req.FraudType,
			AssociationID:  associationID,
			RowStatus:      "active",
			ResponseTimeMs: &responseTimeMs,
		}

		if err := db.CreateFraudData(fraudData); err != nil {
			log.Printf("Failed to store fraud data for identifier %s: %v", identifier.ID, err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to store fraud data")
		}
		
		fraudDataIDs = append(fraudDataIDs, fraudData.ID)
		log.Printf("Created fraud data record with ID: %d for identifier: %s", fraudData.ID, identifier.ID)
	}
	
	log.Printf("Successfully created %d fraud data records with IDs: %v", len(fraudDataIDs), fraudDataIDs)
	if associationID != nil {
		log.Printf("Association created with ID: %d linking all identifiers", *associationID)
	}

	// Create audit log
	auditLog := &database.AuditLog{
		EventType:     "FRAUD_DATA_CREATED",
		InstitutionID: &req.InstitutionID,
		UserID:        req.CreatedBy,
		Metadata: map[string]interface{}{
			"fraud_data_ids":  fraudDataIDs,
			"identifier_type": req.IdentifierType,
			"status":          req.Status,
			"fraud_type":      req.FraudType,
			"association_id":  associationID,
			"identifiers_count": len(allIdentifiers),
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Log the error but don't fail the request
		fmt.Printf("Failed to create audit log: %v\n", err)
	}

	// Prepare response
	response := StoreResponse{
		Success:       true,
		Message:       fmt.Sprintf("Fraud data stored successfully for %d identifiers", len(allIdentifiers)),
		FraudDataID:   fraudDataIDs[0], // Return the first fraud data ID as primary
		AssociationID: associationID,
		CreatedAt:     time.Now().Format(time.RFC3339),
		UpdatedAt:     time.Now().Format(time.RFC3339),
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusCreated,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"Confirmed": true,
		"Suspected": true,
		"Revoked":   true,
	}
	return validStatuses[status]
}

func isValidFraudType(fraudType string) bool {
	validTypes := map[string]bool{
		"Mule":    true,
		"Victim":  true,
		"Fraud":   true,
	}
	return validTypes[fraudType]
}

func main() {
	lambda.Start(handler)
}
