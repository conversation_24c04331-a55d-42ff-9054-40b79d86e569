# SMS Processing Pipeline

A serverless data processing pipeline on AWS for analyzing SMS messages using SAM (Serverless Application Model), Python, and PostgreSQL.

## Architecture Overview

The pipeline consists of three main components connected via SQS queues:

```
S3 File → SQS Input Queue → Lambda 1 (SMS Segregation) → SQS Batch Queue → Lambda 2 (Message Analysis) → SQS Completion Queue → Lambda 3 (Completion Check) → External API
```

### Components

1. **Lambda 1: SMS Segregation** (`lambdas/sms-segregation/`)
   - Reads SMS files from S3
   - Classifies messages as financial/non-financial
   - Stores messages in PostgreSQL database
   - Creates batches and sends to SMS Batch Queue

2. **Lambda 2: Message Analysis** (`lambdas/message-analysis/`)
   - Processes message batches
   - Analyzes messages using OpenAI API
   - Stores analysis results in database
   - Sends completion signals

3. **Lambda 3: Completion Check** (`lambdas/completion-check/`)
   - Checks if all messages for a customer are processed
   - Calls external API when processing is complete

### Database Schema

- **Schema**: `customer_investigation_kb`
- **Tables**:
  - `sms_messages`: Store SMS messages and analysis results
  - `batch_processing`: Track batch processing status
  - `customer_processing_status`: Track customer-level processing status
  - `processing_logs`: Store processing logs and events

## Prerequisites

- AWS CLI configured
- AWS SAM CLI installed
- Python 3.11
- PostgreSQL database (RDS)
- OpenAI API key

## Setup Instructions

### 1. Install Dependencies

```bash
# Install AWS SAM CLI
pip install aws-sam-cli

# Install project dependencies
pip install -r pipeline-requirements.txt
```

### 2. Configure Environment Variables

Create a `.env` file based on `.env.example`:

```bash
make create-env
cp .env.example .env
# Edit .env with your actual values
```

### 3. Initialize Database

```bash
# Run the schema creation script
make init-db
```

### 4. Build and Deploy

```bash
# Validate SAM template
make validate

# Build the application
make sam-build

# Deploy (guided mode for first deployment)
make deploy

# Or deploy without prompts (for CI/CD)
make deploy-ci
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | PostgreSQL database host | `modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com` |
| `DB_PORT` | PostgreSQL database port | `5432` |
| `DB_NAME` | PostgreSQL database name | `postgres` |
| `DB_USERNAME` | PostgreSQL username | `postgres` |
| `DB_PASSWORD` | PostgreSQL password | Required |
| `DB_SCHEMA` | PostgreSQL schema name | `customer_investigation_kb` |
| `BATCH_SIZE` | Number of messages per batch | `50` |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `EXTERNAL_API_ENDPOINT` | External API endpoint for completion notification | Required |

### SAM Parameters

Update `samconfig.toml` with your specific values:

```toml
parameter_overrides = "Environment=\"dev\" DatabaseHost=\"your-db-host\" DatabasePassword=\"your-db-password\" OpenAIApiKey=\"your-openai-key\" ExternalApiEndpoint=\"https://your-api.com/webhook\""
```

## Usage

### 1. Trigger Processing

Send a message to the SMS Input Queue:

```json
{
  "s3_path": "s3://your-bucket/path/to/sms-file.csv",
  "customer_id": "customer_123"
}
```

### 2. Supported File Formats

- **CSV**: Must have columns named `message`, `text`, or `body`
- **JSON**: Array of objects with `message` or `text` fields
- **TXT**: Each line is treated as a separate message

### 3. Monitor Processing

Check CloudWatch Logs for each Lambda function:
- `/aws/lambda/sms-processing-pipeline-SMSSegregationFunction-*`
- `/aws/lambda/sms-processing-pipeline-MessageAnalysisFunction-*`
- `/aws/lambda/sms-processing-pipeline-CompletionCheckFunction-*`

## Development

### Local Testing

```bash
# Start local API
make local-start

# Run tests
make test

# Format code
make format

# Lint code
make lint
```

### Project Structure

```
├── template.yaml              # SAM template
├── schema.sql                # Database schema
├── samconfig.toml            # SAM configuration
├── Makefile                  # Build automation
├── lambdas/                  # Lambda functions
│   ├── sms-segregation/
│   ├── message-analysis/
│   └── completion-check/
├── layers/                   # Lambda layers
│   └── shared/
│       └── python/
│           ├── database.py
│           ├── sms_classifier.py
│           └── utils.py
└── tests/                    # Test files
```

## Monitoring and Observability

### CloudWatch Metrics

- Lambda invocations, errors, and duration
- SQS queue depth and message age
- Dead letter queue messages

### CloudWatch Alarms

Set up alarms for:
- Lambda function errors
- SQS queue depth exceeding thresholds
- Dead letter queue messages

### Logging

All functions log to CloudWatch with structured logging:
- Processing events are logged to the database
- Error details are captured for debugging

## Error Handling

### Dead Letter Queues

Each SQS queue has a corresponding DLQ:
- `sms-input-dlq-{environment}`
- `sms-batch-dlq-{environment}`
- `completion-dlq-{environment}`

### Retry Logic

- SQS messages are retried up to 3 times before moving to DLQ
- Lambda functions have built-in error handling and logging

## Security

### IAM Roles

Each Lambda function has minimal required permissions:
- S3 read access for SMS files
- SQS send/receive permissions
- RDS connect permissions
- CloudWatch Logs write permissions

### Database Security

- Use RDS security groups to restrict access
- Store database credentials in AWS Secrets Manager (recommended)
- Use SSL connections to the database

## Cost Optimization

- Lambda functions use appropriate memory allocation
- SQS queues have message retention policies
- CloudWatch logs have retention periods set

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check security groups and network ACLs
   - Verify database credentials
   - Ensure Lambda functions are in the correct VPC

2. **OpenAI API Errors**
   - Verify API key is correct
   - Check rate limits and quotas
   - Monitor API usage

3. **S3 Access Errors**
   - Verify S3 bucket permissions
   - Check IAM roles for S3 access

### Debugging

1. Check CloudWatch Logs for detailed error messages
2. Monitor SQS queue metrics for processing bottlenecks
3. Use database logs to track processing status

## Deployment Environments

### Development
```bash
sam deploy --parameter-overrides Environment=dev
```

### Staging
```bash
sam deploy --parameter-overrides Environment=staging
```

### Production
```bash
sam deploy --parameter-overrides Environment=prod
```
