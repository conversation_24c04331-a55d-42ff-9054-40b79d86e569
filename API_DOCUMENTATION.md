# API Documentation

## Company Query Details API

### Endpoint
`POST /institution/{institution_id}/query-details`

### Description
Retrieves detailed information about a specific query, including the encryption table that shows the progression of encrypted values through the secure double-blind encryption process.

### Path Parameters
- `institution_id` (required): The ID of the institution that owns the query

### Request Body
```json
{
  "task_id": "uuid-string",
  "query_id": "QRY-YYYYMMDD-XXX"
}
```
Either `task_id` or `query_id` must be provided.

### Response Format
```json
{
  "success": true,
  "message": "Query details retrieved successfully",
  "data": {
    "task_id": "uuid-string",
    "query_id": "QRY-YYYYMMDD-XXX",
    "institution_id": 1,
    "institution_name": "Test Institution",
    "institution_type": "university",
    "status": "completed",
    "response_time_ms": 1500,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:05Z",
    "encryption_table": [
      {
        "row_id": 1,
        "query_id": "550e8400-e29b-41d4-a716-************",
        "original_identifier": "<EMAIL>",
        "identifier_type": "email",
        "encrypted_query": "a1_Bq",
        "intermediate": "a1_Bq_B1",
        "encrypted_by_partner": "a1_B1",
        "consortium": "a1_B1",
        "company_id": "company_1",
        "partner_company_id": "company_2"
      }
    ],
    "original_identifiers": [
      {
        "identifier": "<EMAIL>",
        "type": "email"
      }
    ],
    "fraud_data_results": [
      {
        "id": 123,
        "identifier": "<EMAIL>",
        "identifier_type": "email",
        "status": "Confirmed",
        "fraud_type": "identity_theft",
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "summary": {
      "total_identifiers": 1,
      "total_matches": 1,
      "query_date": "2024-01-15 10:30:00",
      "status": "completed"
    }
  }
}
```

### Encryption Table Structure
The encryption table shows the progression of encrypted values through the secure double-blind encryption process:

| Column | Description | Example |
|--------|-------------|---------|
| `query_id` | Task ID from task log table | `550e8400-e29b-41d4-a716-************` |
| `encrypted_query` | Blinded identifier sent to central server (A_Bq) | `a1_Bq` |
| `intermediate` | Double encrypted value (A_Bq_Bn) | `a1_Bq_B1` |
| `encrypted_by_partner` | Single decrypted value from partner (A_Bn) | `a1_B1` |
| `consortium` | Final value used in consortium lookup (A_Bn) | `a1_B1` |

### Example Requests

#### Using Task ID
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"task_id": "550e8400-e29b-41d4-a716-************"}' \
  https://api.example.com/institution/1/query-details
```

#### Using Query ID
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query_id": "QRY-20240115-001"}' \
  https://api.example.com/institution/1/query-details
```

### Error Responses

#### Missing parameters
```json
{
  "success": false,
  "message": "Missing required field: either task_id or query_id",
  "error": "Missing required field: either task_id or query_id"
}
```

#### Access denied
```json
{
  "success": false,
  "message": "Access denied to this query",
  "error": "Access denied to this query"
}
```

#### Query not found
```json
{
  "success": false,
  "message": "Query response details not found",
  "error": "Query response details not found"
}
```

---

## Company List Task Logs API

### Endpoint
`GET /institution/{institution_id}/task-logs`

### Description
Retrieves a list of past queries and responses from the task_log table for a specific institution with filtering capabilities.

### Path Parameters
- `institution_id` (required): The ID of the institution to fetch task logs for

### Query Parameters
- `status` (optional): Comma-separated list of task statuses to filter by
  - Valid values: `pending`, `processing`, `completed`, `failed`
  - Example: `status=completed,failed`

- `identifier_type` (optional): Comma-separated list of identifier types to filter by
  - Example: `identifier_type=email,phone`

- `task_type` (optional): Comma-separated list of task types to filter by
  - Example: `task_type=query`

- `sort_by` (optional): Field to sort by
  - Valid values: `created_at`, `updated_at`, `status`, `task_type`, `blinded_identifier`, `query_completed_at`
  - Default: `created_at`

- `sort_order` (optional): Sort order
  - Valid values: `asc`, `desc`
  - Default: `desc`

- `limit` (optional): Number of records to return
  - Default: `50`
  - Example: `limit=10`

- `offset` (optional): Number of records to skip for pagination
  - Default: `0`
  - Example: `offset=20`

### Response Format
```json
{
  "success": true,
  "message": "Task logs fetched successfully",
  "taskLogs": [
    {
      "taskId": "uuid-string",
      "taskType": "query",
      "blindedIdentifier": "blinded-identifier-string",
      "identifierType": "email",
      "status": "completed",
      "response": {
        "result": "query-result-data"
      },
      "steps": [
        {
          "step": "step-description",
          "timestamp": "2023-01-01T00:00:00Z"
        }
      ],
      "notes": "Optional notes about the task",
      "error": null,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:01:00Z",
      "createdBy": "user-id",
      "queryCompletedAt": "2023-01-01T00:01:00Z",
      "startTime": "2023-01-01T00:00:00Z",
      "endTime": "2023-01-01T00:01:00Z"
    }
  ],
  "total": 100
}
```

### Example Requests

#### Basic request
```bash
curl -X GET "https://api.example.com/institution/1/task-logs" \
  -H "Content-Type: application/json"
```

#### With filters
```bash
curl -X GET "https://api.example.com/institution/1/task-logs?status=completed,failed&identifier_type=email&limit=10&sort_by=created_at&sort_order=desc" \
  -H "Content-Type: application/json"
```

#### With pagination
```bash
curl -X GET "https://api.example.com/institution/1/task-logs?limit=5&offset=10" \
  -H "Content-Type: application/json"
```

### Error Responses

#### Invalid institution_id
```json
{
  "success": false,
  "error": "Invalid institution_id in path"
}
```

#### Database connection error
```json
{
  "success": false,
  "error": "Database connection failed"
}
```

#### Query error
```json
{
  "success": false,
  "error": "Failed to list task logs"
}
```

### Notes
- The API requires a valid institution_id in the path
- All timestamps are returned in ISO 8601 format (RFC3339)
- The `response` field contains the actual query results and may vary based on the task type
- The `steps` field contains an array of processing steps with timestamps
- Pagination is handled using `limit` and `offset` parameters
- The `total` field in the response indicates the total number of records matching the filters (before pagination) 