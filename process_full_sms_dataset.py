#!/usr/bin/env python3
"""
Complete SMS processing script using hybrid approach.
Processes the entire SMS backup dataset and generates comprehensive results.
"""

import asyncio
import csv
import json
import time
import os
from datetime import datetime
from typing import Dict, List
from hybrid_sms_processor import HybridSMSProcessor

class FullSMSProcessor:
    """Complete SMS processor with progress tracking and batch processing."""
    
    def __init__(self, openai_api_key: str = None, batch_size: int = 50):
        """Initialize the processor."""
        self.processor = HybridSMSProcessor(openai_api_key)
        self.batch_size = batch_size
        self.stats = {
            'total_processed': 0,
            'financial_count': 0,
            'non_financial_count': 0,
            'error_count': 0,
            'openai_calls': 0,
            'processing_time': 0
        }
    
    def map_sms_backup_to_standard(self, row: Dict) -> Dict:
        """Map sms_backup.csv format to standard format."""
        return {
            'original_id': row.get('id', ''),
            'phone_number': row.get('phoneNumber', ''),
            'sender_address': row.get('senderAddress', ''),
            'timestamp': row.get('updateAt', ''),
            'original_text': row.get('text', '')
        }
    
    async def process_batch(self, batch: List[Dict], batch_num: int) -> List[Dict]:
        """Process a batch of messages."""
        print(f"Processing batch {batch_num} ({len(batch)} messages)...")
        
        # Process batch in parallel
        tasks = [self.processor.process_sms(msg) for msg in batch]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions and update stats
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Handle exception
                error_result = {
                    **batch[i],
                    'processing_status': 'error',
                    'error_message': str(result),
                    'classification': 'error',
                    'sms_type': 'Error',
                    'sms_event_subtype': 'Processing Error',
                    'sms_info_type': 'Error'
                }
                processed_results.append(error_result)
                self.stats['error_count'] += 1
            else:
                processed_results.append(result)
                
                # Update stats
                classification = result.get('classification', 'unknown')
                if classification == 'financial':
                    self.stats['financial_count'] += 1
                    # Check if OpenAI was used
                    extracted_data = result.get('extracted_data_json', '{}')
                    if extracted_data != '{}':
                        try:
                            data = json.loads(extracted_data)
                            if len(data) > 5:  # Rich extraction indicates OpenAI
                                self.stats['openai_calls'] += 1
                        except:
                            pass
                elif classification == 'non-financial':
                    self.stats['non_financial_count'] += 1
                elif classification == 'error':
                    self.stats['error_count'] += 1
        
        self.stats['total_processed'] += len(processed_results)
        return processed_results
    
    def print_progress(self, batch_num: int, total_batches: int, elapsed_time: float):
        """Print processing progress."""
        progress = (batch_num / total_batches) * 100
        rate = self.stats['total_processed'] / elapsed_time if elapsed_time > 0 else 0
        
        print(f"\n{'='*60}")
        print(f"PROGRESS: Batch {batch_num}/{total_batches} ({progress:.1f}%)")
        print(f"Processed: {self.stats['total_processed']} messages")
        print(f"Financial: {self.stats['financial_count']}")
        print(f"Non-financial: {self.stats['non_financial_count']}")
        print(f"Errors: {self.stats['error_count']}")
        print(f"OpenAI calls: {self.stats['openai_calls']}")
        print(f"Rate: {rate:.1f} messages/second")
        print(f"Elapsed: {elapsed_time:.1f}s")
        
        if batch_num < total_batches:
            estimated_remaining = (total_batches - batch_num) * (elapsed_time / batch_num)
            print(f"Estimated remaining: {estimated_remaining:.1f}s")
        print(f"{'='*60}\n")
    
    async def process_full_dataset(self, input_file: str, output_file: str):
        """Process the complete SMS dataset."""
        print("FULL SMS DATASET PROCESSING")
        print("="*60)
        print(f"Input: {input_file}")
        print(f"Output: {output_file}")
        print(f"Batch size: {self.batch_size}")
        print("="*60)
        
        # Load all messages
        print("Loading SMS messages...")
        messages = []
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                mapped_row = self.map_sms_backup_to_standard(row)
                messages.append(mapped_row)
        
        total_messages = len(messages)
        total_batches = (total_messages + self.batch_size - 1) // self.batch_size
        
        print(f"Loaded {total_messages} messages")
        print(f"Will process in {total_batches} batches of {self.batch_size}")
        print()
        
        # Process in batches
        all_results = []
        start_time = time.time()
        
        for batch_num in range(1, total_batches + 1):
            batch_start = (batch_num - 1) * self.batch_size
            batch_end = min(batch_start + self.batch_size, total_messages)
            batch = messages[batch_start:batch_end]
            
            try:
                batch_results = await self.process_batch(batch, batch_num)
                all_results.extend(batch_results)
                
                # Print progress every 10 batches or at the end
                if batch_num % 10 == 0 or batch_num == total_batches:
                    elapsed_time = time.time() - start_time
                    self.print_progress(batch_num, total_batches, elapsed_time)
                
            except Exception as e:
                print(f"Error processing batch {batch_num}: {e}")
                # Add error results for this batch
                for msg in batch:
                    error_result = {
                        **msg,
                        'processing_status': 'error',
                        'error_message': f"Batch processing error: {e}",
                        'classification': 'error'
                    }
                    all_results.append(error_result)
                    self.stats['error_count'] += 1
        
        total_time = time.time() - start_time
        self.stats['processing_time'] = total_time
        
        # Save results
        print("Saving results...")
        self.save_results(all_results, output_file)
        
        # Print final summary
        self.print_final_summary(total_time)
        
        return all_results
    
    def save_results(self, results: List[Dict], output_file: str):
        """Save results to CSV with proper field ordering."""
        if not results:
            print("No results to save!")
            return
        
        # Get all possible field names
        all_fields = set()
        for result in results:
            all_fields.update(result.keys())
        
        # Define standard field order (matching sms_processed_results.csv)
        standard_fields = [
            'original_id',
            'phone_number', 
            'sender_address',
            'timestamp',
            'original_text',
            'classification',
            'sms_type',
            'sms_event_subtype', 
            'sms_info_type',
            'extracted_data_json',
            'amount',
            'date',
            'account_number',
            'bank_name',
            'txn_ref',
            'currency',
            'processing_status',
            'error_message'
        ]
        
        # Add OpenAI-specific fields
        openai_fields = [
            'classified_type',
            'classified_subtype', 
            'classified_infotype',
            'entity_name',
            'transaction_amount',
            'available_balance',
            'card_number'
        ]
        
        # Add any additional fields from results
        additional_fields = sorted(list(all_fields - set(standard_fields) - set(openai_fields)))
        
        # Final field order
        fieldnames = standard_fields + openai_fields + additional_fields
        
        # Write CSV
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print(f"Results saved to {output_file}")
        print(f"Total fields: {len(fieldnames)}")
    
    def print_final_summary(self, total_time: float):
        """Print final processing summary."""
        print("\n" + "="*60)
        print("FINAL PROCESSING SUMMARY")
        print("="*60)
        print(f"Total messages processed: {self.stats['total_processed']}")
        print(f"Financial messages: {self.stats['financial_count']} ({self.stats['financial_count']/self.stats['total_processed']*100:.1f}%)")
        print(f"Non-financial messages: {self.stats['non_financial_count']} ({self.stats['non_financial_count']/self.stats['total_processed']*100:.1f}%)")
        print(f"Errors: {self.stats['error_count']} ({self.stats['error_count']/self.stats['total_processed']*100:.1f}%)")
        print(f"OpenAI API calls: {self.stats['openai_calls']}")
        print(f"OpenAI efficiency: {self.stats['openai_calls']}/{self.stats['total_processed']} = {self.stats['openai_calls']/self.stats['total_processed']*100:.1f}%")
        print(f"Processing time: {total_time:.1f} seconds")
        print(f"Average rate: {self.stats['total_processed']/total_time:.1f} messages/second")
        print("="*60)
        
        # Cost estimation
        estimated_cost = self.stats['openai_calls'] * 0.002  # Rough estimate
        print(f"Estimated OpenAI cost: ${estimated_cost:.2f}")
        print("="*60)

async def main():
    """Main function to run full dataset processing."""
    
    # Configuration
    INPUT_FILE = "sms_backup.csv"
    OUTPUT_FILE = "hybrid_sms_processed_results_complete.csv"
    BATCH_SIZE = 50  # Process in batches to manage memory and API limits
    
    # OpenAI API key
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    
    # Check input file
    if not os.path.exists(INPUT_FILE):
        print(f"Error: Input file '{INPUT_FILE}' not found!")
        return
    
    # Initialize processor
    processor = FullSMSProcessor(OPENAI_API_KEY, BATCH_SIZE)
    
    # Confirm processing
    print(f"About to process {INPUT_FILE} with hybrid approach")
    print(f"This will use OpenAI API for financial messages")
    print(f"Estimated cost: $5-15 depending on financial message count")
    print()
    
    response = input("Continue with full processing? (y/n): ").strip().lower()
    if response != 'y':
        print("Processing cancelled.")
        return
    
    # Process dataset
    try:
        results = await processor.process_full_dataset(INPUT_FILE, OUTPUT_FILE)
        print(f"\n✅ Processing completed successfully!")
        print(f"📄 Results saved to: {OUTPUT_FILE}")
        
    except KeyboardInterrupt:
        print("\n❌ Processing interrupted by user")
    except Exception as e:
        print(f"\n❌ Processing failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
