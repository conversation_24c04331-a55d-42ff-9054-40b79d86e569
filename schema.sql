-- Create schema and set search path
CREATE SCHEMA IF NOT EXISTS consortium;
SET search_path TO consortium;

-- institution table
CREATE TABLE IF NOT EXISTS institution (
    id SERIAL PRIMARY KEY,
    institution_name VARCHAR(255) NOT NULL UNIQUE,
    access_role VARCHAR(100) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    institution_type VARCHAR(100),
    encryption_key VARCHAR(128),
    encryption_salt VARCHAR(128),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add institution_type column if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='institution' AND column_name='institution_type'
    ) THEN
        ALTER TABLE institution ADD COLUMN institution_type VARCHAR(100);
    END IF;
END
$$;

-- Add encryption_key column if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='institution' AND column_name='encryption_key'
    ) THEN
        ALTER TABLE institution ADD COLUMN encryption_key VARCHAR(128);
    END IF;
END
$$;

-- Add encryption_salt column if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='institution' AND column_name='encryption_salt'
    ) THEN
        ALTER TABLE institution ADD COLUMN encryption_salt VARCHAR(128);
    END IF;
END
$$;

-- Add identifier_type column to task_log if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='task_log' AND column_name='identifier_type'
    ) THEN
        ALTER TABLE task_log ADD COLUMN identifier_type VARCHAR(50);
    END IF;
END
$$;

-- Add institution_id column to task_log if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='task_log' AND column_name='institution_id'
    ) THEN
        ALTER TABLE task_log ADD COLUMN institution_id INTEGER REFERENCES institution(id) ON DELETE CASCADE;
    END IF;
END
$$;

-- institution indexes
CREATE INDEX IF NOT EXISTS idx_institution_name ON institution(institution_name);
CREATE INDEX IF NOT EXISTS idx_institution_status ON institution(status);

-- id_associations table
CREATE TABLE IF NOT EXISTS id_associations (
    id SERIAL PRIMARY KEY,
    associations JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_id_associations_gin ON id_associations USING GIN(associations);

-- fraud_data table
CREATE TABLE IF NOT EXISTS fraud_data (
    id SERIAL PRIMARY KEY,
    identifier TEXT NOT NULL,
    identifier_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    institution_id INTEGER NOT NULL REFERENCES institution(id) ON DELETE CASCADE,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL ,
    fraud_type VARCHAR(50) NOT NULL ,
    updated_by VARCHAR(255),
    association_id INTEGER REFERENCES id_associations(id) ON DELETE SET NULL,
    row_status VARCHAR(50) DEFAULT 'active',
    response_time_ms INTEGER
);

-- Add row_status column if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='fraud_data' AND column_name='row_status'
    ) THEN
        ALTER TABLE fraud_data ADD COLUMN row_status VARCHAR(50) DEFAULT 'Not Started';
    END IF;
END
$$;

-- Add response_time_ms column if missing (for upgrades)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='fraud_data' AND column_name='response_time_ms'
    ) THEN
        ALTER TABLE fraud_data ADD COLUMN response_time_ms INTEGER;
    END IF;
END
$$;

-- fraud_data indexes
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier ON fraud_data(identifier);
CREATE INDEX IF NOT EXISTS idx_fraud_data_institution_id ON fraud_data(institution_id);
CREATE INDEX IF NOT EXISTS idx_fraud_data_status ON fraud_data(status);
CREATE INDEX IF NOT EXISTS idx_fraud_data_fraud_type ON fraud_data(fraud_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier_type ON fraud_data(identifier_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_created_at ON fraud_data(created_at);
CREATE INDEX IF NOT EXISTS idx_fraud_data_association_id ON fraud_data(association_id);
CREATE INDEX IF NOT EXISTS idx_fraud_data_metadata_gin ON fraud_data USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_fraud_data_row_status ON fraud_data(row_status);

-- audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    institution_id INTEGER REFERENCES institution(id) ON DELETE SET NULL,
    user_id VARCHAR(255) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- audit_logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_institution_id ON audit_logs(institution_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_metadata_gin ON audit_logs USING GIN(metadata);

-- task_log table
CREATE TABLE IF NOT EXISTS task_log (
    id SERIAL PRIMARY KEY,
    task_id UUID NOT NULL UNIQUE,
    task_type VARCHAR(50) NOT NULL DEFAULT 'query',
    blinded_identifiers JSONB DEFAULT '[]'::jsonb,
    institution_id INTEGER REFERENCES institution(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    response JSONB,
    steps JSONB DEFAULT '[]'::jsonb,
    notes TEXT,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    query_completed_at TIMESTAMP WITH TIME ZONE,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE
);

-- Add migration to convert existing single identifier to array format
DO $$
BEGIN
    -- Check if blinded_identifiers column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name='task_log' AND column_name='blinded_identifiers'
    ) THEN
        -- Add the new column
        ALTER TABLE task_log ADD COLUMN blinded_identifiers JSONB DEFAULT '[]'::jsonb;
        
        -- Migrate existing data from blinded_identifier and identifier_type to blinded_identifiers
        UPDATE task_log 
        SET blinded_identifiers = CASE 
            WHEN blinded_identifier IS NOT NULL AND blinded_identifier != '' THEN
                CASE 
                    WHEN identifier_type IS NOT NULL THEN
                        jsonb_build_array(jsonb_build_object('identifier', blinded_identifier, 'type', identifier_type))
                    ELSE
                        jsonb_build_array(jsonb_build_object('identifier', blinded_identifier, 'type', 'unknown'))
                END
            ELSE '[]'::jsonb
        END;
        
        -- Drop the old columns
        ALTER TABLE task_log DROP COLUMN IF EXISTS blinded_identifier;
        ALTER TABLE task_log DROP COLUMN IF EXISTS identifier_type;
    END IF;
END
$$;

-- task_log indexes
CREATE INDEX IF NOT EXISTS idx_task_log_task_id ON task_log(task_id);
CREATE INDEX IF NOT EXISTS idx_task_log_status ON task_log(status);
CREATE INDEX IF NOT EXISTS idx_task_log_task_type ON task_log(task_type);
CREATE INDEX IF NOT EXISTS idx_task_log_created_by ON task_log(created_by);
CREATE INDEX IF NOT EXISTS idx_task_log_created_at ON task_log(created_at);
CREATE INDEX IF NOT EXISTS idx_task_log_response_gin ON task_log USING GIN(response);
CREATE INDEX IF NOT EXISTS idx_task_log_steps_gin ON task_log USING GIN(steps);
CREATE INDEX IF NOT EXISTS idx_task_log_institution_id ON task_log(institution_id);
CREATE INDEX IF NOT EXISTS idx_task_log_blinded_identifiers_gin ON task_log USING GIN(blinded_identifiers);

-- query_response_details table - NEW TABLE for storing detailed query response data
CREATE TABLE IF NOT EXISTS query_response_details (
    id SERIAL PRIMARY KEY,
    task_id UUID NOT NULL REFERENCES task_log(task_id) ON DELETE CASCADE,
    query_id VARCHAR(100) NOT NULL,
    institution_id INTEGER NOT NULL REFERENCES institution(id) ON DELETE CASCADE,
    original_identifiers JSONB NOT NULL, -- Original plain text identifiers with types
    blinded_identifiers JSONB NOT NULL, -- Blinded identifiers sent to central server
    double_encrypted_values JSONB NOT NULL, -- Double encrypted values from all companies
    single_decrypted_values JSONB NOT NULL, -- Single decrypted values from all companies
    fraud_data_results JSONB NOT NULL, -- Final fraud data results
    response_summary JSONB NOT NULL, -- Summary statistics and metadata
    response_timeline JSONB NOT NULL, -- Timeline of events
    response_processes JSONB NOT NULL, -- Process steps and status
    response_stats JSONB NOT NULL, -- Statistical data
    response_time_ms INTEGER NOT NULL, -- Response time in milliseconds
    status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- query_response_details indexes
CREATE INDEX IF NOT EXISTS idx_query_response_details_task_id ON query_response_details(task_id);
CREATE INDEX IF NOT EXISTS idx_query_response_details_query_id ON query_response_details(query_id);
CREATE INDEX IF NOT EXISTS idx_query_response_details_institution_id ON query_response_details(institution_id);
CREATE INDEX IF NOT EXISTS idx_query_response_details_status ON query_response_details(status);
CREATE INDEX IF NOT EXISTS idx_query_response_details_created_at ON query_response_details(created_at);
CREATE INDEX IF NOT EXISTS idx_query_response_details_original_identifiers_gin ON query_response_details USING GIN(original_identifiers);
CREATE INDEX IF NOT EXISTS idx_query_response_details_blinded_identifiers_gin ON query_response_details USING GIN(blinded_identifiers);
CREATE INDEX IF NOT EXISTS idx_query_response_details_double_encrypted_values_gin ON query_response_details USING GIN(double_encrypted_values);
CREATE INDEX IF NOT EXISTS idx_query_response_details_single_decrypted_values_gin ON query_response_details USING GIN(single_decrypted_values);
CREATE INDEX IF NOT EXISTS idx_query_response_details_fraud_data_results_gin ON query_response_details USING GIN(fraud_data_results);

-- Common trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_institution_updated_at 
    BEFORE UPDATE ON institution 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fraud_data_updated_at 
    BEFORE UPDATE ON fraud_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_id_associations_updated_at 
    BEFORE UPDATE ON id_associations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audit_logs_updated_at 
    BEFORE UPDATE ON audit_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_log_updated_at 
    BEFORE UPDATE ON task_log 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_response_details_updated_at 
    BEFORE UPDATE ON query_response_details 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Seed data for testing
INSERT INTO institution (institution_name, access_role, created_by, status, institution_type)
VALUES ('Test Institution', 'admin', 'system', 'active', 'university')
ON CONFLICT (institution_name) DO NOTHING;
